import React from 'react';
import type { BidStatus } from '../../types';
import { getStatusColor, getStatusText } from '../../utils/helpers';

interface StatusBadgeProps {
  status: BidStatus;
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className = '' }) => {
  return (
    <span className={`status-badge ${getStatusColor(status)} ${className}`}>
      {getStatusText(status)}
    </span>
  );
};

export default StatusBadge;
