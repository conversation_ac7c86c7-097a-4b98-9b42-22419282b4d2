import React, { useState } from 'react';
import { Search, Loader2, Calendar } from 'lucide-react';
import type { FormMInfo } from '../../types';

interface FormMSectionProps {
  values: FormMInfo;
  errors: any;
  touched: any;
  setFieldValue: (field: string, value: any) => void;
  setFieldTouched: (field: string, touched?: boolean) => void;
}

const FormMSection: React.FC<FormMSectionProps> = ({
  values,
  errors,
  touched,
  setFieldValue,
  setFieldTouched,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleFormMLookup = async () => {
    if (!values.refId) return;
    
    setIsLoading(true);
    
    // Mock API call to fetch Form M details
    setTimeout(() => {
      // Mock data - replace with actual API call
      const mockFormMData = {
        applicantName: 'ABC Trading Company Limited',
        applicantAddress: '123 Business District, Lagos, Nigeria',
        beneficiaryName: 'XYZ International Suppliers',
        applicantTin: '12345678-0001',
        rcNumber: 'RC123456',
        applicantPhone: '+***********-5678',
        countryOfSupply: 'China',
        hsCode: '8517.12.00',
        currency: 'USD',
        totalCFValue: 150000.00,
        formMRegistrationDate: '2024-01-15',
        generalGoodsDescription: 'Mobile phones and accessories',
        paymentMode: 'Letter of Credit',
        fundApplicationId: 'FA-2024-001234',
      };
      
      Object.entries(mockFormMData).forEach(([key, value]) => {
        setFieldValue(`formMInfo.${key}`, value);
      });
      
      setIsLoading(false);
    }, 1500);
  };

  const currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY'];
  const paymentModes = [
    'Letter of Credit',
    'Documentary Collection',
    'Open Account',
    'Cash Against Documents',
    'Advance Payment'
  ];

  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Form M Information</h2>
        <span className="text-sm text-gray-500">Step 2 of 4</span>
      </div>

      <div className="space-y-6">
        {/* Form M Reference ID */}
        <div>
          <label htmlFor="refId" className="block text-sm font-medium text-gray-700 mb-2">
            Form M Reference ID *
          </label>
          <div className="flex space-x-2">
            <input
              id="refId"
              type="text"
              value={values.refId}
              onChange={(e) => setFieldValue('formMInfo.refId', e.target.value)}
              onBlur={() => setFieldTouched('formMInfo.refId', true)}
              className={`input-field flex-1 ${
                touched.refId && errors.refId
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="Enter Form M Reference ID"
            />
            <button
              type="button"
              onClick={handleFormMLookup}
              disabled={!values.refId || isLoading}
              className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Search className="w-4 h-4" />
              )}
              <span>Go</span>
            </button>
          </div>
          {touched.refId && errors.refId && (
            <p className="mt-1 text-sm text-red-600">{errors.refId}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Applicant's Name */}
          <div>
            <label htmlFor="applicantName" className="block text-sm font-medium text-gray-700 mb-2">
              Applicant's Name *
            </label>
            <input
              id="applicantName"
              type="text"
              value={values.applicantName}
              onChange={(e) => setFieldValue('formMInfo.applicantName', e.target.value)}
              onBlur={() => setFieldTouched('formMInfo.applicantName', true)}
              className={`input-field ${
                touched.applicantName && errors.applicantName
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="Will be populated from Form M"
              readOnly
            />
            {touched.applicantName && errors.applicantName && (
              <p className="mt-1 text-sm text-red-600">{errors.applicantName}</p>
            )}
          </div>

          {/* Beneficiary's Name */}
          <div>
            <label htmlFor="beneficiaryName" className="block text-sm font-medium text-gray-700 mb-2">
              Beneficiary's Name *
            </label>
            <input
              id="beneficiaryName"
              type="text"
              value={values.beneficiaryName}
              onChange={(e) => setFieldValue('formMInfo.beneficiaryName', e.target.value)}
              onBlur={() => setFieldTouched('formMInfo.beneficiaryName', true)}
              className={`input-field ${
                touched.beneficiaryName && errors.beneficiaryName
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="Will be populated from Form M"
              readOnly
            />
            {touched.beneficiaryName && errors.beneficiaryName && (
              <p className="mt-1 text-sm text-red-600">{errors.beneficiaryName}</p>
            )}
          </div>

          {/* Applicant's TIN */}
          <div>
            <label htmlFor="applicantTin" className="block text-sm font-medium text-gray-700 mb-2">
              Applicant's TIN Number *
            </label>
            <input
              id="applicantTin"
              type="text"
              value={values.applicantTin}
              onChange={(e) => setFieldValue('formMInfo.applicantTin', e.target.value)}
              onBlur={() => setFieldTouched('formMInfo.applicantTin', true)}
              className={`input-field ${
                touched.applicantTin && errors.applicantTin
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="Will be populated from Form M"
              readOnly
            />
            {touched.applicantTin && errors.applicantTin && (
              <p className="mt-1 text-sm text-red-600">{errors.applicantTin}</p>
            )}
          </div>

          {/* RC Number */}
          <div>
            <label htmlFor="rcNumber" className="block text-sm font-medium text-gray-700 mb-2">
              RC Number *
            </label>
            <input
              id="rcNumber"
              type="text"
              value={values.rcNumber}
              onChange={(e) => setFieldValue('formMInfo.rcNumber', e.target.value)}
              onBlur={() => setFieldTouched('formMInfo.rcNumber', true)}
              className={`input-field ${
                touched.rcNumber && errors.rcNumber
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="Will be populated from Form M"
              readOnly
            />
            {touched.rcNumber && errors.rcNumber && (
              <p className="mt-1 text-sm text-red-600">{errors.rcNumber}</p>
            )}
          </div>

          {/* Phone Number */}
          <div>
            <label htmlFor="applicantPhone" className="block text-sm font-medium text-gray-700 mb-2">
              Applicant Phone Number *
            </label>
            <input
              id="applicantPhone"
              type="tel"
              value={values.applicantPhone}
              onChange={(e) => setFieldValue('formMInfo.applicantPhone', e.target.value)}
              onBlur={() => setFieldTouched('formMInfo.applicantPhone', true)}
              className={`input-field ${
                touched.applicantPhone && errors.applicantPhone
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="Will be populated from Form M"
              readOnly
            />
            {touched.applicantPhone && errors.applicantPhone && (
              <p className="mt-1 text-sm text-red-600">{errors.applicantPhone}</p>
            )}
          </div>

          {/* Country of Supply */}
          <div>
            <label htmlFor="countryOfSupply" className="block text-sm font-medium text-gray-700 mb-2">
              Country of Supply *
            </label>
            <input
              id="countryOfSupply"
              type="text"
              value={values.countryOfSupply}
              onChange={(e) => setFieldValue('formMInfo.countryOfSupply', e.target.value)}
              onBlur={() => setFieldTouched('formMInfo.countryOfSupply', true)}
              className={`input-field ${
                touched.countryOfSupply && errors.countryOfSupply
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="Will be populated from Form M"
              readOnly
            />
            {touched.countryOfSupply && errors.countryOfSupply && (
              <p className="mt-1 text-sm text-red-600">{errors.countryOfSupply}</p>
            )}
          </div>

          {/* HS Code */}
          <div>
            <label htmlFor="hsCode" className="block text-sm font-medium text-gray-700 mb-2">
              HS Code *
            </label>
            <input
              id="hsCode"
              type="text"
              value={values.hsCode}
              onChange={(e) => setFieldValue('formMInfo.hsCode', e.target.value)}
              onBlur={() => setFieldTouched('formMInfo.hsCode', true)}
              className={`input-field ${
                touched.hsCode && errors.hsCode
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="Will be populated from Form M"
              readOnly
            />
            {touched.hsCode && errors.hsCode && (
              <p className="mt-1 text-sm text-red-600">{errors.hsCode}</p>
            )}
          </div>

          {/* Currency */}
          <div>
            <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
              Currency *
            </label>
            <select
              id="currency"
              value={values.currency}
              onChange={(e) => setFieldValue('formMInfo.currency', e.target.value)}
              onBlur={() => setFieldTouched('formMInfo.currency', true)}
              className={`input-field ${
                touched.currency && errors.currency
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
            >
              {currencies.map((currency) => (
                <option key={currency} value={currency}>
                  {currency}
                </option>
              ))}
            </select>
            {touched.currency && errors.currency && (
              <p className="mt-1 text-sm text-red-600">{errors.currency}</p>
            )}
          </div>

          {/* Total C&F Value */}
          <div>
            <label htmlFor="totalCFValue" className="block text-sm font-medium text-gray-700 mb-2">
              Total C&F Value *
            </label>
            <input
              id="totalCFValue"
              type="number"
              step="0.01"
              value={values.totalCFValue}
              onChange={(e) => setFieldValue('formMInfo.totalCFValue', parseFloat(e.target.value) || 0)}
              onBlur={() => setFieldTouched('formMInfo.totalCFValue', true)}
              className={`input-field ${
                touched.totalCFValue && errors.totalCFValue
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="0.00"
              readOnly
            />
            {touched.totalCFValue && errors.totalCFValue && (
              <p className="mt-1 text-sm text-red-600">{errors.totalCFValue}</p>
            )}
          </div>

          {/* Form M Registration Date */}
          <div>
            <label htmlFor="formMRegistrationDate" className="block text-sm font-medium text-gray-700 mb-2">
              Form M Registration Date *
            </label>
            <div className="relative">
              <input
                id="formMRegistrationDate"
                type="date"
                value={values.formMRegistrationDate}
                onChange={(e) => setFieldValue('formMInfo.formMRegistrationDate', e.target.value)}
                onBlur={() => setFieldTouched('formMInfo.formMRegistrationDate', true)}
                className={`input-field ${
                  touched.formMRegistrationDate && errors.formMRegistrationDate
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                    : ''
                }`}
                readOnly
              />
              <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
            </div>
            {touched.formMRegistrationDate && errors.formMRegistrationDate && (
              <p className="mt-1 text-sm text-red-600">{errors.formMRegistrationDate}</p>
            )}
          </div>

          {/* Payment Mode */}
          <div>
            <label htmlFor="paymentMode" className="block text-sm font-medium text-gray-700 mb-2">
              Payment Mode / Fund Application ID *
            </label>
            <select
              id="paymentMode"
              value={values.paymentMode}
              onChange={(e) => setFieldValue('formMInfo.paymentMode', e.target.value)}
              onBlur={() => setFieldTouched('formMInfo.paymentMode', true)}
              className={`input-field ${
                touched.paymentMode && errors.paymentMode
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
            >
              <option value="">Select payment mode</option>
              {paymentModes.map((mode) => (
                <option key={mode} value={mode}>
                  {mode}
                </option>
              ))}
            </select>
            {touched.paymentMode && errors.paymentMode && (
              <p className="mt-1 text-sm text-red-600">{errors.paymentMode}</p>
            )}
          </div>

          {/* Fund Application ID */}
          <div>
            <label htmlFor="fundApplicationId" className="block text-sm font-medium text-gray-700 mb-2">
              Fund Application ID *
            </label>
            <input
              id="fundApplicationId"
              type="text"
              value={values.fundApplicationId}
              onChange={(e) => setFieldValue('formMInfo.fundApplicationId', e.target.value)}
              onBlur={() => setFieldTouched('formMInfo.fundApplicationId', true)}
              className={`input-field ${
                touched.fundApplicationId && errors.fundApplicationId
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="Will be populated from Form M"
              readOnly
            />
            {touched.fundApplicationId && errors.fundApplicationId && (
              <p className="mt-1 text-sm text-red-600">{errors.fundApplicationId}</p>
            )}
          </div>
        </div>

        {/* Applicant's Address */}
        <div>
          <label htmlFor="applicantAddress" className="block text-sm font-medium text-gray-700 mb-2">
            Applicant's Address *
          </label>
          <textarea
            id="applicantAddress"
            rows={3}
            value={values.applicantAddress}
            onChange={(e) => setFieldValue('formMInfo.applicantAddress', e.target.value)}
            onBlur={() => setFieldTouched('formMInfo.applicantAddress', true)}
            className={`input-field ${
              touched.applicantAddress && errors.applicantAddress
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : ''
            }`}
            placeholder="Will be populated from Form M"
            readOnly
          />
          {touched.applicantAddress && errors.applicantAddress && (
            <p className="mt-1 text-sm text-red-600">{errors.applicantAddress}</p>
          )}
        </div>

        {/* General Goods Description */}
        <div>
          <label htmlFor="generalGoodsDescription" className="block text-sm font-medium text-gray-700 mb-2">
            General Goods Description *
          </label>
          <textarea
            id="generalGoodsDescription"
            rows={3}
            value={values.generalGoodsDescription}
            onChange={(e) => setFieldValue('formMInfo.generalGoodsDescription', e.target.value)}
            onBlur={() => setFieldTouched('formMInfo.generalGoodsDescription', true)}
            className={`input-field ${
              touched.generalGoodsDescription && errors.generalGoodsDescription
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : ''
            }`}
            placeholder="Will be populated from Form M"
            readOnly
          />
          {touched.generalGoodsDescription && errors.generalGoodsDescription && (
            <p className="mt-1 text-sm text-red-600">{errors.generalGoodsDescription}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default FormMSection;
