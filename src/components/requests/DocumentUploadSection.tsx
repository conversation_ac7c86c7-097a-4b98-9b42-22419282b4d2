import React, { useRef } from 'react';
import { Upload, X, FileText, AlertCircle } from 'lucide-react';
import type { DocumentUpload } from '../../types';
import { getFileSize, isValidFileType, generateId } from '../../utils/helpers';

interface DocumentUploadSectionProps {
  documents: DocumentUpload[];
  onDocumentsChange: (documents: DocumentUpload[]) => void;
}

const DocumentUploadSection: React.FC<DocumentUploadSectionProps> = ({
  documents,
  onDocumentsChange,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const maxFiles = 4;
  const maxFileSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    if (documents.length + files.length > maxFiles) {
      alert(`You can only upload a maximum of ${maxFiles} documents.`);
      return;
    }

    const newDocuments: DocumentUpload[] = [];
    const errors: string[] = [];

    files.forEach((file) => {
      // Validate file type
      if (!isValidFileType(file, allowedTypes)) {
        errors.push(`${file.name}: Invalid file type. Please upload PDF, Word, Excel, or image files.`);
        return;
      }

      // Validate file size
      if (file.size > maxFileSize) {
        errors.push(`${file.name}: File size exceeds 10MB limit.`);
        return;
      }

      // Create document object
      const document: DocumentUpload = {
        id: generateId(),
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        uploadedAt: new Date().toISOString(),
        url: URL.createObjectURL(file), // In real app, this would be uploaded to server
      };

      newDocuments.push(document);
    });

    if (errors.length > 0) {
      alert(errors.join('\n'));
    }

    if (newDocuments.length > 0) {
      onDocumentsChange([...documents, ...newDocuments]);
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemoveDocument = (documentId: string) => {
    const updatedDocuments = documents.filter(doc => doc.id !== documentId);
    onDocumentsChange(updatedDocuments);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word')) return '📝';
    if (fileType.includes('excel') || fileType.includes('sheet')) return '📊';
    if (fileType.includes('image')) return '🖼️';
    return '📎';
  };

  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Document Upload</h2>
        <span className="text-sm text-gray-500">Step 4 of 4</span>
      </div>

      {/* Upload Area */}
      <div className="mb-6">
        <div
          onClick={handleUploadClick}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            documents.length >= maxFiles
              ? 'border-gray-200 bg-gray-50 cursor-not-allowed'
              : 'border-purple-300 hover:border-purple-400 hover:bg-purple-50'
          }`}
        >
          <Upload className={`mx-auto h-12 w-12 mb-4 ${
            documents.length >= maxFiles ? 'text-gray-400' : 'text-purple-400'
          }`} />
          
          <div className="space-y-2">
            <p className={`text-lg font-medium ${
              documents.length >= maxFiles ? 'text-gray-500' : 'text-gray-900'
            }`}>
              {documents.length >= maxFiles 
                ? 'Maximum files reached' 
                : 'Click to upload documents'
              }
            </p>
            
            <p className="text-sm text-gray-500">
              {documents.length >= maxFiles 
                ? `You have uploaded ${documents.length} of ${maxFiles} allowed files`
                : `Upload up to ${maxFiles - documents.length} more files (Max 10MB each)`
              }
            </p>
            
            <p className="text-xs text-gray-400">
              Supported formats: PDF, Word, Excel, JPEG, PNG
            </p>
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
          onChange={handleFileSelect}
          className="hidden"
          disabled={documents.length >= maxFiles}
        />
      </div>

      {/* Uploaded Documents List */}
      {documents.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-900">
            Uploaded Documents ({documents.length}/{maxFiles})
          </h3>
          
          <div className="space-y-2">
            {documents.map((document) => (
              <div
                key={document.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getFileIcon(document.fileType)}</span>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {document.fileName}
                    </p>
                    <p className="text-xs text-gray-500">
                      {getFileSize(document.fileSize)} • Uploaded {new Date(document.uploadedAt).toLocaleString()}
                    </p>
                  </div>
                </div>
                
                <button
                  onClick={() => handleRemoveDocument(document.id)}
                  className="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded"
                  title="Remove document"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Requirements Notice */}
      <div className="mt-6 p-4 bg-amber-50 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <AlertCircle className="h-5 w-5 text-amber-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-amber-800">
              Document Requirements
            </h3>
            <div className="mt-2 text-sm text-amber-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Maximum of {maxFiles} documents allowed</li>
                <li>Each file must be less than 10MB</li>
                <li>Accepted formats: PDF, Word (.doc, .docx), Excel (.xls, .xlsx), Images (JPEG, PNG)</li>
                <li>Ensure all documents are clear and legible</li>
                <li>Include all relevant supporting documents for your bid request</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Upload Guidelines */}
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <FileText className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Recommended Documents
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Form M certificate</li>
                <li>Proforma invoice</li>
                <li>Import permit (if applicable)</li>
                <li>Bank guarantee or letter of credit</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentUploadSection;
