import React from 'react';
import { Send, FileText, DollarSign, CreditCard, Upload } from 'lucide-react';
import Modal from '../common/Modal';
import type { AccountInfo, FormMInfo, RateInfo, DocumentUpload } from '../../types';
import { formatCurrency, formatDate, getFileSize } from '../../utils/helpers';

interface ReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
  formData: {
    accountInfo: AccountInfo;
    formMInfo: FormMInfo;
    rateInfo: RateInfo;
    documents: DocumentUpload[];
  };
  isSubmitting: boolean;
}

const ReviewModal: React.FC<ReviewModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  formData,
  isSubmitting,
}) => {
  const { accountInfo, formMInfo, rateInfo, documents } = formData;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Review Bid Request" size="xl">
      <div className="space-y-6">
        {/* Account Information */}
        <div className="border rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <CreditCard className="w-5 h-5 text-primary-600" />
            <h3 className="text-lg font-medium text-gray-900">Account Information</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Account Number:</span>
              <p className="text-gray-900">{accountInfo.accountNumber}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Account Name:</span>
              <p className="text-gray-900">{accountInfo.accountName}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">BVN:</span>
              <p className="text-gray-900">{accountInfo.bvn}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Account Balance:</span>
              <p className="text-gray-900">{formatCurrency(accountInfo.accountBalance, 'NGN')}</p>
            </div>
          </div>
        </div>

        {/* Form M Information */}
        <div className="border rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <FileText className="w-5 h-5 text-primary-600" />
            <h3 className="text-lg font-medium text-gray-900">Form M Information</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Reference ID:</span>
              <p className="text-gray-900">{formMInfo.refId}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Applicant Name:</span>
              <p className="text-gray-900">{formMInfo.applicantName}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Beneficiary Name:</span>
              <p className="text-gray-900">{formMInfo.beneficiaryName}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">TIN Number:</span>
              <p className="text-gray-900">{formMInfo.applicantTin}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">RC Number:</span>
              <p className="text-gray-900">{formMInfo.rcNumber}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Phone Number:</span>
              <p className="text-gray-900">{formMInfo.applicantPhone}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Country of Supply:</span>
              <p className="text-gray-900">{formMInfo.countryOfSupply}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">HS Code:</span>
              <p className="text-gray-900">{formMInfo.hsCode}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Currency:</span>
              <p className="text-gray-900">{formMInfo.currency}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Total C&F Value:</span>
              <p className="text-gray-900">{formatCurrency(formMInfo.totalCFValue, formMInfo.currency)}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Registration Date:</span>
              <p className="text-gray-900">{formatDate(formMInfo.formMRegistrationDate)}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Payment Mode:</span>
              <p className="text-gray-900">{formMInfo.paymentMode}</p>
            </div>
            <div className="md:col-span-2">
              <span className="font-medium text-gray-700">Applicant Address:</span>
              <p className="text-gray-900">{formMInfo.applicantAddress}</p>
            </div>
            <div className="md:col-span-2">
              <span className="font-medium text-gray-700">Goods Description:</span>
              <p className="text-gray-900">{formMInfo.generalGoodsDescription}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Fund Application ID:</span>
              <p className="text-gray-900">{formMInfo.fundApplicationId}</p>
            </div>
          </div>
        </div>

        {/* Rate Information */}
        <div className="border rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <DollarSign className="w-5 h-5 text-primary-600" />
            <h3 className="text-lg font-medium text-gray-900">Rate Information</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Amount:</span>
              <p className="text-gray-900">{formatCurrency(rateInfo.amount, formMInfo.currency)}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Exchange Rate:</span>
              <p className="text-gray-900">{rateInfo.rate.toFixed(4)}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Tolerance (10%):</span>
              <p className="text-gray-900">{formatCurrency(rateInfo.tolerance, formMInfo.currency)}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">110% of Total C&F:</span>
              <p className="text-gray-900">{formatCurrency(rateInfo.calculatedValue, formMInfo.currency)}</p>
            </div>
            <div className="md:col-span-2">
              <span className="font-medium text-gray-700">Naira Equivalent:</span>
              <p className="text-gray-900 text-lg font-semibold">
                {formatCurrency(rateInfo.amount * rateInfo.rate, 'NGN')}
              </p>
            </div>
          </div>
        </div>

        {/* Documents */}
        <div className="border rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Upload className="w-5 h-5 text-primary-600" />
            <h3 className="text-lg font-medium text-gray-900">
              Uploaded Documents ({documents.length})
            </h3>
          </div>
          {documents.length > 0 ? (
            <div className="space-y-2">
              {documents.map((document) => (
                <div key={document.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center space-x-2">
                    <FileText className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-900">{document.fileName}</span>
                  </div>
                  <span className="text-xs text-gray-500">{getFileSize(document.fileSize)}</span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500">No documents uploaded</p>
          )}
        </div>

        {/* Summary */}
        <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-primary-900 mb-2">Request Summary</h3>
          <div className="text-sm text-primary-800 space-y-1">
            <p>
              <span className="font-medium">Applicant:</span> {formMInfo.applicantName}
            </p>
            <p>
              <span className="font-medium">Transaction Amount:</span> {formatCurrency(rateInfo.amount, formMInfo.currency)}
            </p>
            <p>
              <span className="font-medium">Naira Equivalent:</span> {formatCurrency(rateInfo.amount * rateInfo.rate, 'NGN')}
            </p>
            <p>
              <span className="font-medium">Documents:</span> {documents.length} file(s) attached
            </p>
          </div>
        </div>

        {/* Confirmation */}
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-amber-800">
                Please Review Carefully
              </h3>
              <div className="mt-2 text-sm text-amber-700">
                <p>
                  Once submitted, this request will be sent to the Trade Team for review. 
                  Please ensure all information is accurate and complete.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            disabled={isSubmitting}
            className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Back to Edit
          </button>
          <button
            type="button"
            onClick={onSubmit}
            disabled={isSubmitting}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isSubmitting ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
            <span>{isSubmitting ? 'Submitting...' : 'Submit Request'}</span>
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default ReviewModal;
