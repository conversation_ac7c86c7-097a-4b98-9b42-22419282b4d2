import React from 'react';
import { Calculator, Info } from 'lucide-react';
import type { RateInfo } from '../../types';
import { formatCurrency } from '../../utils/helpers';

interface RateSectionProps {
  values: RateInfo;
  errors: any;
  touched: any;
  setFieldValue: (field: string, value: any) => void;
  setFieldTouched: (field: string, touched?: boolean) => void;
}

const RateSection: React.FC<RateSectionProps> = ({
  values,
  errors,
  touched,
  setFieldValue,
  setFieldTouched,
}) => {
  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Rate Information</h2>
        <span className="text-sm text-gray-500">Step 3 of 4</span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Amount */}
        <div>
          <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
            Amount *
          </label>
          <input
            id="amount"
            type="number"
            step="0.01"
            value={values.amount}
            onChange={(e) => setFieldValue('rateInfo.amount', parseFloat(e.target.value) || 0)}
            onBlur={() => setFieldTouched('rateInfo.amount', true)}
            className={`input-field ${
              touched.amount && errors.amount
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : ''
            }`}
            placeholder="Enter amount"
          />
          {touched.amount && errors.amount && (
            <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
          )}
        </div>

        {/* Rate */}
        <div>
          <label htmlFor="rate" className="block text-sm font-medium text-gray-700 mb-2">
            Exchange Rate *
          </label>
          <input
            id="rate"
            type="number"
            step="0.0001"
            value={values.rate}
            onChange={(e) => setFieldValue('rateInfo.rate', parseFloat(e.target.value) || 0)}
            onBlur={() => setFieldTouched('rateInfo.rate', true)}
            className={`input-field ${
              touched.rate && errors.rate
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : ''
            }`}
            placeholder="Enter exchange rate"
          />
          {touched.rate && errors.rate && (
            <p className="mt-1 text-sm text-red-600">{errors.rate}</p>
          )}
          <p className="mt-1 text-sm text-gray-500">
            Current exchange rate for the selected currency
          </p>
        </div>

        {/* Tolerance (Auto-calculated) */}
        <div>
          <label htmlFor="tolerance" className="block text-sm font-medium text-gray-700 mb-2">
            Tolerance (10%)
          </label>
          <div className="relative">
            <input
              id="tolerance"
              type="number"
              step="0.01"
              value={values.tolerance}
              className="input-field bg-gray-50"
              placeholder="Auto-calculated"
              readOnly
            />
            <Calculator className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          </div>
          <p className="mt-1 text-sm text-gray-500">
            Automatically calculated as 10% of the amount
          </p>
        </div>

        {/* Calculated Value (110% of Total C&F Value) */}
        <div>
          <label htmlFor="calculatedValue" className="block text-sm font-medium text-gray-700 mb-2">
            Calculated Value (110% of Total C&F)
          </label>
          <div className="relative">
            <input
              id="calculatedValue"
              type="number"
              step="0.01"
              value={values.calculatedValue}
              className="input-field bg-gray-50"
              placeholder="Auto-calculated"
              readOnly
            />
            <Calculator className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          </div>
          <p className="mt-1 text-sm text-gray-500">
            Automatically calculated as 110% of Total C&F Value
          </p>
        </div>
      </div>

      {/* Calculation Summary */}
      {(values.amount > 0 || values.tolerance > 0 || values.calculatedValue > 0) && (
        <div className="mt-6 p-4 bg-purple-50 rounded-lg">
          <div className="flex items-start">
            <Info className="h-5 w-5 text-purple-400 mt-0.5 mr-3 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-purple-800 mb-2">
                Rate Calculation Summary
              </h3>
              <div className="space-y-2 text-sm text-purple-700">
                {values.amount > 0 && (
                  <div className="flex justify-between">
                    <span>Amount:</span>
                    <span className="font-medium">{formatCurrency(values.amount)}</span>
                  </div>
                )}
                {values.rate > 0 && (
                  <div className="flex justify-between">
                    <span>Exchange Rate:</span>
                    <span className="font-medium">{values.rate.toFixed(4)}</span>
                  </div>
                )}
                {values.tolerance > 0 && (
                  <div className="flex justify-between">
                    <span>Tolerance (10%):</span>
                    <span className="font-medium">{formatCurrency(values.tolerance)}</span>
                  </div>
                )}
                {values.calculatedValue > 0 && (
                  <div className="flex justify-between border-t border-purple-200 pt-2">
                    <span>110% of Total C&F Value:</span>
                    <span className="font-medium">{formatCurrency(values.calculatedValue)}</span>
                  </div>
                )}
                {values.amount > 0 && values.rate > 0 && (
                  <div className="flex justify-between border-t border-purple-200 pt-2">
                    <span>Naira Equivalent:</span>
                    <span className="font-medium">
                      {formatCurrency(values.amount * values.rate, 'NGN')}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <Info className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Rate Information Guidelines
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Enter the transaction amount in the foreign currency</li>
                <li>Provide the current exchange rate for conversion to Naira</li>
                <li>Tolerance is automatically calculated as 10% of the amount</li>
                <li>The system calculates 110% of the Total C&F Value for reference</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RateSection;
