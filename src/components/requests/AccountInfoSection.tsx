import React, { useState } from 'react';
import { Search, Loader2 } from 'lucide-react';
import type { AccountInfo } from '../../types';
import { formatCurrency } from '../../utils/helpers';

interface AccountInfoSectionProps {
  values: AccountInfo;
  errors: any;
  touched: any;
  setFieldValue: (field: string, value: any) => void;
  setFieldTouched: (field: string, touched?: boolean) => void;
}

const AccountInfoSection: React.FC<AccountInfoSectionProps> = ({
  values,
  errors,
  touched,
  setFieldValue,
  setFieldTouched,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleAccountLookup = async () => {
    if (!values.accountNumber) return;
    
    setIsLoading(true);
    
    // Mock API call to fetch account details
    setTimeout(() => {
      // Mock data - replace with actual API call
      const mockAccountData = {
        accountName: 'John Doe Trading Ltd',
        bvn: '***********',
        accountBalance: 2500000.00,
      };
      
      setFieldValue('accountInfo.accountName', mockAccountData.accountName);
      setFieldValue('accountInfo.bvn', mockAccountData.bvn);
      setFieldValue('accountInfo.accountBalance', mockAccountData.accountBalance);
      
      setIsLoading(false);
    }, 1500);
  };

  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Account Information</h2>
        <span className="text-sm text-gray-500">Step 1 of 4</span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Account Number */}
        <div className="md:col-span-2">
          <label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700 mb-2">
            Account Number *
          </label>
          <div className="flex space-x-2">
            <input
              id="accountNumber"
              type="text"
              value={values.accountNumber}
              onChange={(e) => setFieldValue('accountInfo.accountNumber', e.target.value)}
              onBlur={() => setFieldTouched('accountInfo.accountNumber', true)}
              className={`input-field flex-1 ${
                touched.accountNumber && errors.accountNumber
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="Enter account number"
            />
            <button
              type="button"
              onClick={handleAccountLookup}
              disabled={!values.accountNumber || isLoading}
              className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Search className="w-4 h-4" />
              )}
              <span>Go</span>
            </button>
          </div>
          {touched.accountNumber && errors.accountNumber && (
            <p className="mt-1 text-sm text-red-600">{errors.accountNumber}</p>
          )}
        </div>

        {/* Account Name */}
        <div>
          <label htmlFor="accountName" className="block text-sm font-medium text-gray-700 mb-2">
            Account Name *
          </label>
          <input
            id="accountName"
            type="text"
            value={values.accountName}
            onChange={(e) => setFieldValue('accountInfo.accountName', e.target.value)}
            onBlur={() => setFieldTouched('accountInfo.accountName', true)}
            className={`input-field ${
              touched.accountName && errors.accountName
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : ''
            }`}
            placeholder="Account name will be populated"
            readOnly
          />
          {touched.accountName && errors.accountName && (
            <p className="mt-1 text-sm text-red-600">{errors.accountName}</p>
          )}
        </div>

        {/* BVN */}
        <div>
          <label htmlFor="bvn" className="block text-sm font-medium text-gray-700 mb-2">
            BVN *
          </label>
          <input
            id="bvn"
            type="text"
            value={values.bvn}
            onChange={(e) => setFieldValue('accountInfo.bvn', e.target.value)}
            onBlur={() => setFieldTouched('accountInfo.bvn', true)}
            className={`input-field ${
              touched.bvn && errors.bvn
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : ''
            }`}
            placeholder="BVN will be populated"
            readOnly
          />
          {touched.bvn && errors.bvn && (
            <p className="mt-1 text-sm text-red-600">{errors.bvn}</p>
          )}
        </div>

        {/* Account Balance */}
        <div>
          <label htmlFor="accountBalance" className="block text-sm font-medium text-gray-700 mb-2">
            Account Balance *
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₦</span>
            <input
              id="accountBalance"
              type="number"
              value={values.accountBalance}
              onChange={(e) => setFieldValue('accountInfo.accountBalance', parseFloat(e.target.value) || 0)}
              onBlur={() => setFieldTouched('accountInfo.accountBalance', true)}
              className={`input-field pl-8 ${
                touched.accountBalance && errors.accountBalance
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : ''
              }`}
              placeholder="0.00"
              readOnly
            />
          </div>
          {values.accountBalance > 0 && (
            <p className="mt-1 text-sm text-gray-600">
              Balance: {formatCurrency(values.accountBalance, 'NGN')}
            </p>
          )}
          {touched.accountBalance && errors.accountBalance && (
            <p className="mt-1 text-sm text-red-600">{errors.accountBalance}</p>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <Search className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              How to use Account Lookup
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                Enter the account number and click "Go" to automatically populate the account name, BVN, and current balance.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountInfoSection;
