import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import LoginPage from './components/auth/LoginPage';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Layout from './components/layout/Layout';
import Dashboard from './pages/Dashboard';
import UserManagement from './pages/UserManagement';
import NewRequest from './pages/NewRequest';
import RequestDetails from './pages/RequestDetails';
import MyRequests from './pages/MyRequests';
import TradeReview from './pages/TradeReview';
import TreasuryReview from './pages/TreasuryReview';
import AuditLogs from './pages/AuditLogs';
import AllRequests from './pages/AllRequests';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          <Route path="/login" element={<LoginPage />} />

          <Route path="/" element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />

            {/* RM Routes */}
            <Route path="requests/new" element={
              <ProtectedRoute allowedRoles={['RM']}>
                <NewRequest />
              </ProtectedRoute>
            } />
            <Route path="requests/:id" element={
              <ProtectedRoute allowedRoles={['RM', 'TRADE_TEAM', 'TREASURY', 'SGC', 'AUDITOR']}>
                <RequestDetails />
              </ProtectedRoute>
            } />
            <Route path="requests" element={
              <ProtectedRoute allowedRoles={['RM']}>
                <MyRequests />
              </ProtectedRoute>
            } />

            {/* Trade Team Routes */}
            <Route path="trade-review" element={
              <ProtectedRoute allowedRoles={['TRADE_TEAM']}>
                <TradeReview />
              </ProtectedRoute>
            } />

            {/* Treasury Routes */}
            <Route path="treasury-review" element={
              <ProtectedRoute allowedRoles={['TREASURY']}>
                <TreasuryReview />
              </ProtectedRoute>
            } />

            {/* SGC Routes */}
            <Route path="users" element={
              <ProtectedRoute allowedRoles={['SGC']}>
                <UserManagement />
              </ProtectedRoute>
            } />

            {/* Auditor Routes */}
            <Route path="audit-logs" element={
              <ProtectedRoute allowedRoles={['AUDITOR']}>
                <AuditLogs />
              </ProtectedRoute>
            } />

            {/* Admin Routes */}
            <Route path="all-requests" element={
              <ProtectedRoute allowedRoles={['SGC', 'AUDITOR']}>
                <AllRequests />
              </ProtectedRoute>
            } />
          </Route>
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
