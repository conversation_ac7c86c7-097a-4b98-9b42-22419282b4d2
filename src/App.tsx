import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import LoginPage from './components/auth/LoginPage';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Layout from './components/layout/Layout';
import UserManagement from './pages/UserManagement';
import NewRequest from './pages/NewRequest';

// Placeholder components - will be implemented in subsequent tasks
const Dashboard = () => <div className="p-6"><h1 className="text-2xl font-bold">Dashboard</h1></div>;
const MyRequests = () => <div className="p-6"><h1 className="text-2xl font-bold">My Requests</h1></div>;
const TradeReview = () => <div className="p-6"><h1 className="text-2xl font-bold">Trade Review</h1></div>;
const TreasuryReview = () => <div className="p-6"><h1 className="text-2xl font-bold">Treasury Review</h1></div>;
const AuditLogs = () => <div className="p-6"><h1 className="text-2xl font-bold">Audit Logs</h1></div>;
const AllRequests = () => <div className="p-6"><h1 className="text-2xl font-bold">All Requests</h1></div>;

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          <Route path="/login" element={<LoginPage />} />

          <Route path="/" element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />

            {/* RM Routes */}
            <Route path="requests/new" element={
              <ProtectedRoute allowedRoles={['RM']}>
                <NewRequest />
              </ProtectedRoute>
            } />
            <Route path="requests" element={
              <ProtectedRoute allowedRoles={['RM']}>
                <MyRequests />
              </ProtectedRoute>
            } />

            {/* Trade Team Routes */}
            <Route path="trade-review" element={
              <ProtectedRoute allowedRoles={['TRADE_TEAM']}>
                <TradeReview />
              </ProtectedRoute>
            } />

            {/* Treasury Routes */}
            <Route path="treasury-review" element={
              <ProtectedRoute allowedRoles={['TREASURY']}>
                <TreasuryReview />
              </ProtectedRoute>
            } />

            {/* SGC Routes */}
            <Route path="users" element={
              <ProtectedRoute allowedRoles={['SGC']}>
                <UserManagement />
              </ProtectedRoute>
            } />

            {/* Auditor Routes */}
            <Route path="audit-logs" element={
              <ProtectedRoute allowedRoles={['AUDITOR']}>
                <AuditLogs />
              </ProtectedRoute>
            } />

            {/* Admin Routes */}
            <Route path="all-requests" element={
              <ProtectedRoute allowedRoles={['SGC', 'AUDITOR']}>
                <AllRequests />
              </ProtectedRoute>
            } />
          </Route>
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
