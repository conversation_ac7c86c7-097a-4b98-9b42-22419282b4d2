import React, { useState, useEffect } from 'react';
import { Search, Download, Eye, Upload, CheckCircle, XCircle } from 'lucide-react';
import Table from '../components/common/Table';
import StatusBadge from '../components/common/StatusBadge';
import Modal from '../components/common/Modal';
import ConfirmDialog from '../components/common/ConfirmDialog';
import type { BidRequest, BidStatus } from '../types';
import { exportBidRequests, readExcelFile } from '../utils/export';
import { formatCurrency, formatDate, searchItems, getFileSize } from '../utils/helpers';

const TreasuryReview: React.FC = () => {
  const [requests, setRequests] = useState<BidRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<BidRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<BidStatus | 'ALL'>('ALL');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedRequest, setSelectedRequest] = useState<BidRequest | null>(null);
  const [isFinalizeModalOpen, setIsFinalizeModalOpen] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isSuccessful, setIsSuccessful] = useState(true);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const itemsPerPage = 10;

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockRequests: BidRequest[] = [
      {
        id: 'BID-2024-001',
        rmId: '1',
        accountInfo: {
          accountNumber: '**********',
          accountName: 'ABC Trading Ltd',
          bvn: '***********',
          accountBalance: 2500000,
        },
        formMInfo: {
          refId: 'FM-2024-001',
          applicantName: 'ABC Trading Ltd',
          applicantAddress: '123 Business District, Lagos',
          beneficiaryName: 'XYZ International',
          applicantTin: '********-0001',
          rcNumber: 'RC123456',
          applicantPhone: '+234-************',
          countryOfSupply: 'China',
          hsCode: '8517.12.00',
          currency: 'USD',
          totalCFValue: 150000,
          formMRegistrationDate: '2024-01-15',
          generalGoodsDescription: 'Mobile phones and accessories',
          paymentMode: 'Letter of Credit',
          fundApplicationId: 'FA-2024-001234',
        },
        rateInfo: {
          amount: 150000,
          rate: 1580.50,
          tolerance: 15000,
          calculatedValue: 165000,
        },
        documents: [],
        status: 'TRADE_APPROVED',
        createdAt: '2024-01-20T10:30:00Z',
        updatedAt: '2024-01-22T14:20:00Z',
        tradeTeamReview: {
          id: 'tr-001',
          reviewerId: '2',
          transactionReference: 'TXN-2024-001',
          status: 'APPROVED',
          comment: 'All documents verified and approved',
          reviewedAt: '2024-01-22T14:20:00Z',
        },
      },
      {
        id: 'BID-2024-006',
        rmId: '1',
        accountInfo: {
          accountNumber: '********88',
          accountName: 'PQR Global Trade',
          bvn: '********889',
          accountBalance: 7800000,
        },
        formMInfo: {
          refId: 'FM-2024-006',
          applicantName: 'PQR Global Trade',
          applicantAddress: '987 Export Plaza, Lagos',
          beneficiaryName: 'International Commodities Inc',
          applicantTin: '********-0006',
          rcNumber: 'RC445566',
          applicantPhone: '+234-************',
          countryOfSupply: 'Brazil',
          hsCode: '1701.14.00',
          currency: 'USD',
          totalCFValue: 320000,
          formMRegistrationDate: '2024-01-22',
          generalGoodsDescription: 'Raw sugar and agricultural products',
          paymentMode: 'Letter of Credit',
          fundApplicationId: 'FA-2024-006789',
        },
        rateInfo: {
          amount: 320000,
          rate: 1575.80,
          tolerance: 32000,
          calculatedValue: 352000,
        },
        documents: [],
        status: 'TREASURY_REVIEW',
        createdAt: '2024-01-24T09:20:00Z',
        updatedAt: '2024-01-24T15:45:00Z',
        tradeTeamReview: {
          id: 'tr-006',
          reviewerId: '2',
          transactionReference: 'TXN-2024-006',
          status: 'APPROVED',
          comment: 'Trade documentation complete and verified',
          reviewedAt: '2024-01-24T15:45:00Z',
        },
      },
    ];

    setTimeout(() => {
      setRequests(mockRequests);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and search requests
  useEffect(() => {
    let filtered = [...requests];

    // Apply status filter
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(request => request.status === statusFilter);
    }

    // Apply search
    if (searchTerm) {
      filtered = searchItems(filtered, searchTerm, ['id', 'formMInfo.applicantName', 'formMInfo.refId']);
    }

    setFilteredRequests(filtered);
    setCurrentPage(1);
  }, [requests, searchTerm, statusFilter]);

  const handleFinalize = (request: BidRequest) => {
    setSelectedRequest(request);
    setUploadedFile(null);
    setIsSuccessful(true);
    setComment('');
    setIsFinalizeModalOpen(true);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        alert('Please upload an Excel file (.xlsx or .xls)');
        return;
      }
      
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('File size must be less than 10MB');
        return;
      }

      setUploadedFile(file);
    }
  };

  const handleSubmitFinalization = () => {
    if (!selectedRequest) return;
    
    if (!uploadedFile) {
      alert('Please upload an Excel file');
      return;
    }

    setIsFinalizeModalOpen(false);
    setIsConfirmDialogOpen(true);
  };

  const handleConfirmFinalization = async () => {
    if (!selectedRequest || !uploadedFile) return;

    setIsSubmitting(true);

    try {
      // Read Excel file (in real app, this would be processed on the server)
      const excelData = await readExcelFile(uploadedFile);
      console.log('Excel data:', excelData);

      // Mock API call
      setTimeout(() => {
        const updatedRequests = requests.map(request => {
          if (request.id === selectedRequest.id) {
            return {
              ...request,
              status: isSuccessful ? 'COMPLETED' as BidStatus : 'UNSUCCESSFUL' as BidStatus,
              treasuryReview: {
                id: `treasury-${Date.now()}`,
                reviewerId: 'current-user-id',
                excelFile: {
                  id: `file-${Date.now()}`,
                  fileName: uploadedFile.name,
                  fileSize: uploadedFile.size,
                  fileType: uploadedFile.type,
                  uploadedAt: new Date().toISOString(),
                  url: URL.createObjectURL(uploadedFile),
                },
                isSuccessful: isSuccessful,
                finalizedAt: new Date().toISOString(),
                comment: comment,
              },
              updatedAt: new Date().toISOString(),
            };
          }
          return request;
        });

        setRequests(updatedRequests);
        setIsSubmitting(false);
        setIsConfirmDialogOpen(false);
        setSelectedRequest(null);
        setUploadedFile(null);
      }, 2000);
    } catch (error) {
      console.error('Error processing Excel file:', error);
      alert('Error processing Excel file. Please try again.');
      setIsSubmitting(false);
    }
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    exportBidRequests(filteredRequests, format);
  };

  const columns = [
    {
      key: 'id',
      header: 'Request ID',
      sortable: true,
    },
    {
      key: 'formMInfo.applicantName',
      header: 'Applicant',
      render: (request: BidRequest) => (
        <div>
          <div className="font-medium">{request.formMInfo.applicantName}</div>
          <div className="text-xs text-gray-500">{request.formMInfo.refId}</div>
        </div>
      ),
    },
    {
      key: 'tradeTeamReview.transactionReference',
      header: 'Trade Reference',
      render: (request: BidRequest) => (
        <div className="text-sm">
          {request.tradeTeamReview?.transactionReference || '-'}
        </div>
      ),
    },
    {
      key: 'formMInfo.currency',
      header: 'Amount',
      render: (request: BidRequest) => (
        <div>
          <div className="font-medium">
            {formatCurrency(request.rateInfo.amount, request.formMInfo.currency)}
          </div>
          <div className="text-xs text-gray-500">
            ≈ {formatCurrency(request.rateInfo.amount * request.rateInfo.rate, 'NGN')}
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      render: (request: BidRequest) => <StatusBadge status={request.status} />,
    },
    {
      key: 'updatedAt',
      header: 'Last Updated',
      render: (request: BidRequest) => formatDate(request.updatedAt),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (request: BidRequest) => (
        <div className="flex items-center space-x-2">
          <button
            className="p-1 text-primary-600 hover:text-primary-800"
            title="View details"
          >
            <Eye className="w-4 h-4" />
          </button>
          {(request.status === 'TRADE_APPROVED' || request.status === 'TREASURY_REVIEW') && (
            <button
              onClick={() => handleFinalize(request)}
              className="p-1 text-success-600 hover:text-success-800"
              title="Finalize request"
            >
              <CheckCircle className="w-4 h-4" />
            </button>
          )}
        </div>
      ),
    },
  ];

  const totalPages = Math.ceil(filteredRequests.length / itemsPerPage);
  const paginatedRequests = filteredRequests.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Treasury Review</h1>
          <p className="text-gray-600">Finalize approved bid confirmation requests</p>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search requests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as BidStatus | 'ALL')}
              className="input-field"
            >
              <option value="ALL">All Status</option>
              <option value="TRADE_APPROVED">Trade Approved</option>
              <option value="TREASURY_REVIEW">Under Review</option>
              <option value="FINALIZED">Finalized</option>
              <option value="COMPLETED">Completed</option>
              <option value="UNSUCCESSFUL">Unsuccessful</option>
            </select>
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Export</label>
            <div className="flex space-x-2">
              <button
                onClick={() => handleExport('excel')}
                className="btn-secondary flex items-center space-x-1 text-sm"
              >
                <Download className="w-4 h-4" />
                <span>Excel</span>
              </button>
              <button
                onClick={() => handleExport('pdf')}
                className="btn-secondary flex items-center space-x-1 text-sm"
              >
                <Download className="w-4 h-4" />
                <span>PDF</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Requests Table */}
      <Table
        data={paginatedRequests}
        columns={columns}
        loading={loading}
        emptyMessage="No requests found"
        pagination={{
          currentPage,
          totalPages,
          onPageChange: setCurrentPage,
        }}
      />

      {/* Finalization Modal */}
      <Modal
        isOpen={isFinalizeModalOpen}
        onClose={() => setIsFinalizeModalOpen(false)}
        title="Finalize Request"
        size="md"
      >
        <div className="space-y-4">
          {selectedRequest && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Request Details</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p><span className="font-medium">ID:</span> {selectedRequest.id}</p>
                <p><span className="font-medium">Applicant:</span> {selectedRequest.formMInfo.applicantName}</p>
                <p><span className="font-medium">Amount:</span> {formatCurrency(selectedRequest.rateInfo.amount, selectedRequest.formMInfo.currency)}</p>
                <p><span className="font-medium">Trade Reference:</span> {selectedRequest.tradeTeamReview?.transactionReference}</p>
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload Excel File *
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <input
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileUpload}
                className="hidden"
                id="excel-upload"
              />
              <label
                htmlFor="excel-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Upload className="w-8 h-8 text-gray-400 mb-2" />
                <span className="text-sm text-gray-600">Click to upload Excel file</span>
                <span className="text-xs text-gray-400">Supports .xlsx and .xls files (max 10MB)</span>
              </label>
            </div>
            {uploadedFile && (
              <div className="mt-2 p-2 bg-green-50 rounded border border-green-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600">✓</span>
                    <span className="text-sm text-green-800">{uploadedFile.name}</span>
                  </div>
                  <span className="text-xs text-green-600">{getFileSize(uploadedFile.size)}</span>
                </div>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="status"
                  checked={isSuccessful}
                  onChange={() => setIsSuccessful(true)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Successful</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="status"
                  checked={!isSuccessful}
                  onChange={() => setIsSuccessful(false)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Unsuccessful</span>
              </label>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Comment (Optional)
            </label>
            <textarea
              rows={3}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              className="input-field"
              placeholder="Enter finalization comment..."
            />
          </div>

          <div className="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={() => setIsFinalizeModalOpen(false)}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSubmitFinalization}
              className="btn-success"
            >
              Finalize Request
            </button>
          </div>
        </div>
      </Modal>

      {/* Confirmation Dialog */}
      <ConfirmDialog
        isOpen={isConfirmDialogOpen}
        onClose={() => setIsConfirmDialogOpen(false)}
        onConfirm={handleConfirmFinalization}
        title="Confirm Finalization"
        message={`Are you sure you want to finalize this request as ${isSuccessful ? 'successful' : 'unsuccessful'}? This action cannot be undone.`}
        type="success"
        confirmText="Finalize"
        loading={isSubmitting}
      />
    </div>
  );
};

export default TreasuryReview;
