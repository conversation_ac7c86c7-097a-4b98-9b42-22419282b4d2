import React, { useState, useEffect } from 'react';
import { Search, Download, Eye, CheckCircle, XCircle, MessageSquare } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Table from '../components/common/Table';
import StatusBadge from '../components/common/StatusBadge';
import Modal from '../components/common/Modal';
import ConfirmDialog from '../components/common/ConfirmDialog';
import type { BidRequest, BidStatus } from '../types';
import { exportBidRequests } from '../utils/export';
import { formatCurrency, formatDate, searchItems } from '../utils/helpers';

const TradeReview: React.FC = () => {
  const navigate = useNavigate();
  const [requests, setRequests] = useState<BidRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<BidRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<BidStatus | 'ALL'>('ALL');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedRequest, setSelectedRequest] = useState<BidRequest | null>(null);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject'>('approve');
  const [transactionRef, setTransactionRef] = useState('');
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const itemsPerPage = 10;

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockRequests: BidRequest[] = [
      {
        id: 'BID-2024-004',
        rmId: '1',
        accountInfo: {
          accountNumber: '********66',
          accountName: 'JKL Import Export',
          bvn: '***********',
          accountBalance: 4500000,
        },
        formMInfo: {
          refId: 'FM-2024-004',
          applicantName: 'JKL Import Export',
          applicantAddress: '321 Trade Center, Lagos',
          beneficiaryName: 'European Suppliers Ltd',
          applicantTin: '********-0004',
          rcNumber: 'RC223344',
          applicantPhone: '+234-************',
          countryOfSupply: 'Italy',
          hsCode: '6204.62.00',
          currency: 'EUR',
          totalCFValue: 95000,
          formMRegistrationDate: '2024-01-20',
          generalGoodsDescription: 'Textile and clothing materials',
          paymentMode: 'Letter of Credit',
          fundApplicationId: 'FA-2024-004567',
        },
        rateInfo: {
          amount: 95000,
          rate: 1720.25,
          tolerance: 9500,
          calculatedValue: 104500,
        },
        documents: [],
        status: 'TRADE_REVIEW',
        createdAt: '2024-01-23T08:30:00Z',
        updatedAt: '2024-01-23T08:30:00Z',
      },
      {
        id: 'BID-2024-005',
        rmId: '1',
        accountInfo: {
          accountNumber: '**********',
          accountName: 'MNO Technologies',
          bvn: '***********',
          accountBalance: 6200000,
        },
        formMInfo: {
          refId: 'FM-2024-005',
          applicantName: 'MNO Technologies',
          applicantAddress: '654 Tech Hub, Abuja',
          beneficiaryName: 'Silicon Valley Innovations',
          applicantTin: '********-0005',
          rcNumber: 'RC334455',
          applicantPhone: '+234-************',
          countryOfSupply: 'USA',
          hsCode: '8471.70.00',
          currency: 'USD',
          totalCFValue: 180000,
          formMRegistrationDate: '2024-01-21',
          generalGoodsDescription: 'Computer servers and networking equipment',
          paymentMode: 'Documentary Collection',
          fundApplicationId: 'FA-2024-005678',
        },
        rateInfo: {
          amount: 180000,
          rate: 1580.50,
          tolerance: 18000,
          calculatedValue: 198000,
        },
        documents: [],
        status: 'SUBMITTED',
        createdAt: '2024-01-23T11:15:00Z',
        updatedAt: '2024-01-23T11:15:00Z',
      },
    ];

    setTimeout(() => {
      setRequests(mockRequests);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and search requests
  useEffect(() => {
    let filtered = [...requests];

    // Apply status filter
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(request => request.status === statusFilter);
    }

    // Apply search
    if (searchTerm) {
      filtered = searchItems(filtered, searchTerm, ['id', 'formMInfo.applicantName', 'formMInfo.refId']);
    }

    setFilteredRequests(filtered);
    setCurrentPage(1);
  }, [requests, searchTerm, statusFilter]);

  const handleReview = (request: BidRequest, action: 'approve' | 'reject') => {
    setSelectedRequest(request);
    setReviewAction(action);
    setTransactionRef('');
    setComment('');
    setIsReviewModalOpen(true);
  };

  const handleSubmitReview = () => {
    if (!selectedRequest) return;
    
    if (reviewAction === 'approve' && !transactionRef.trim()) {
      alert('Transaction reference is required for approval');
      return;
    }

    setIsReviewModalOpen(false);
    setIsConfirmDialogOpen(true);
  };

  const handleConfirmReview = async () => {
    if (!selectedRequest) return;

    setIsSubmitting(true);

    // Mock API call
    setTimeout(() => {
      const updatedRequests = requests.map(request => {
        if (request.id === selectedRequest.id) {
          return {
            ...request,
            status: reviewAction === 'approve' ? 'TRADE_APPROVED' as BidStatus : 'TRADE_REJECTED' as BidStatus,
            tradeTeamReview: {
              id: `tr-${Date.now()}`,
              reviewerId: 'current-user-id',
              transactionReference: transactionRef,
              status: reviewAction === 'approve' ? 'APPROVED' as const : 'REJECTED' as const,
              comment: comment,
              reviewedAt: new Date().toISOString(),
            },
            updatedAt: new Date().toISOString(),
          };
        }
        return request;
      });

      setRequests(updatedRequests);
      setIsSubmitting(false);
      setIsConfirmDialogOpen(false);
      setSelectedRequest(null);
    }, 2000);
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    exportBidRequests(filteredRequests, format);
  };

  const handleViewRequest = (request: BidRequest) => {
    navigate(`/requests/${request.id}`);
  };

  const columns = [
    {
      key: 'id',
      header: 'Request ID',
      sortable: true,
    },
    {
      key: 'formMInfo.applicantName',
      header: 'Applicant',
      render: (request: BidRequest) => (
        <div>
          <div className="font-medium">{request.formMInfo.applicantName}</div>
          <div className="text-xs text-gray-500">{request.formMInfo.refId}</div>
        </div>
      ),
    },
    {
      key: 'formMInfo.currency',
      header: 'Amount',
      render: (request: BidRequest) => (
        <div>
          <div className="font-medium">
            {formatCurrency(request.rateInfo.amount, request.formMInfo.currency)}
          </div>
          <div className="text-xs text-gray-500">
            ≈ {formatCurrency(request.rateInfo.amount * request.rateInfo.rate, 'NGN')}
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      render: (request: BidRequest) => <StatusBadge status={request.status} />,
    },
    {
      key: 'createdAt',
      header: 'Submitted',
      render: (request: BidRequest) => formatDate(request.createdAt),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (request: BidRequest) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleViewRequest(request)}
            className="p-1 text-primary-600 hover:text-primary-800"
            title="View details"
          >
            <Eye className="w-4 h-4" />
          </button>
          {(request.status === 'SUBMITTED' || request.status === 'TRADE_REVIEW') && (
            <>
              <button
                onClick={() => handleReview(request, 'approve')}
                className="p-1 text-success-600 hover:text-success-800"
                title="Approve request"
              >
                <CheckCircle className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleReview(request, 'reject')}
                className="p-1 text-danger-600 hover:text-danger-800"
                title="Reject request"
              >
                <XCircle className="w-4 h-4" />
              </button>
            </>
          )}
        </div>
      ),
    },
  ];

  const totalPages = Math.ceil(filteredRequests.length / itemsPerPage);
  const paginatedRequests = filteredRequests.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Trade Review</h1>
          <p className="text-gray-600">Review and approve bid confirmation requests</p>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search requests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as BidStatus | 'ALL')}
              className="input-field"
            >
              <option value="ALL">All Status</option>
              <option value="SUBMITTED">Submitted</option>
              <option value="TRADE_REVIEW">Under Review</option>
              <option value="TRADE_APPROVED">Approved</option>
              <option value="TRADE_REJECTED">Rejected</option>
            </select>
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Export</label>
            <div className="flex space-x-2">
              <button
                onClick={() => handleExport('excel')}
                className="btn-secondary flex items-center space-x-1 text-sm"
              >
                <Download className="w-4 h-4" />
                <span>Excel</span>
              </button>
              <button
                onClick={() => handleExport('pdf')}
                className="btn-secondary flex items-center space-x-1 text-sm"
              >
                <Download className="w-4 h-4" />
                <span>PDF</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Requests Table */}
      <Table
        data={paginatedRequests}
        columns={columns}
        loading={loading}
        emptyMessage="No requests found"
        pagination={{
          currentPage,
          totalPages,
          totalItems: filteredRequests.length,
          itemsPerPage,
          onPageChange: setCurrentPage,
        }}
      />

      {/* Review Modal */}
      <Modal
        isOpen={isReviewModalOpen}
        onClose={() => setIsReviewModalOpen(false)}
        title={`${reviewAction === 'approve' ? 'Approve' : 'Reject'} Request`}
        size="md"
      >
        <div className="space-y-4">
          {selectedRequest && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Request Details</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p><span className="font-medium">ID:</span> {selectedRequest.id}</p>
                <p><span className="font-medium">Applicant:</span> {selectedRequest.formMInfo.applicantName}</p>
                <p><span className="font-medium">Amount:</span> {formatCurrency(selectedRequest.rateInfo.amount, selectedRequest.formMInfo.currency)}</p>
              </div>
            </div>
          )}

          {reviewAction === 'approve' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Transaction Reference *
              </label>
              <input
                type="text"
                value={transactionRef}
                onChange={(e) => setTransactionRef(e.target.value)}
                className="input-field"
                placeholder="Enter transaction reference number"
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Comment {reviewAction === 'reject' ? '*' : '(Optional)'}
            </label>
            <textarea
              rows={3}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              className="input-field"
              placeholder={`Enter ${reviewAction === 'approve' ? 'approval' : 'rejection'} comment...`}
            />
          </div>

          <div className="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={() => setIsReviewModalOpen(false)}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSubmitReview}
              className={reviewAction === 'approve' ? 'btn-success' : 'btn-danger'}
            >
              {reviewAction === 'approve' ? 'Approve Request' : 'Reject Request'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Confirmation Dialog */}
      <ConfirmDialog
        isOpen={isConfirmDialogOpen}
        onClose={() => setIsConfirmDialogOpen(false)}
        onConfirm={handleConfirmReview}
        title={`Confirm ${reviewAction === 'approve' ? 'Approval' : 'Rejection'}`}
        message={`Are you sure you want to ${reviewAction} this request? This action cannot be undone.`}
        type={reviewAction === 'approve' ? 'success' : 'danger'}
        confirmText={reviewAction === 'approve' ? 'Approve' : 'Reject'}
        loading={isSubmitting}
      />
    </div>
  );
};

export default TradeReview;
