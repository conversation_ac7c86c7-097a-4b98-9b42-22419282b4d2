import React, { useState, useEffect } from 'react';
import { Plus, Search, Download, Eye, Edit, Filter } from 'lucide-react';
import { Link } from 'react-router-dom';
import Table from '../components/common/Table';
import StatusBadge from '../components/common/StatusBadge';
import RequestDetailsModal from '../components/requests/RequestDetailsModal';
import type { BidRequest, BidStatus } from '../types';
import { exportBidRequests } from '../utils/export';
import { formatCurrency, formatDate, searchItems } from '../utils/helpers';
import { useAuth } from '../contexts/AuthContext';

const MyRequests: React.FC = () => {
  const { user } = useAuth();
  const [requests, setRequests] = useState<BidRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<BidRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<BidStatus | 'ALL'>('ALL');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedRequest, setSelectedRequest] = useState<BidRequest | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' }>({
    key: 'createdAt',
    direction: 'desc',
  });

  const itemsPerPage = 10;

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockRequests: BidRequest[] = [
      {
        id: 'BID-2024-001',
        rmId: user?.id || '1',
        accountInfo: {
          accountNumber: '**********',
          accountName: 'ABC Trading Ltd',
          bvn: '***********',
          accountBalance: 2500000,
        },
        formMInfo: {
          refId: 'FM-2024-001',
          applicantName: 'ABC Trading Ltd',
          applicantAddress: '123 Business District, Lagos',
          beneficiaryName: 'XYZ International',
          applicantTin: '********-0001',
          rcNumber: 'RC123456',
          applicantPhone: '+234-************',
          countryOfSupply: 'China',
          hsCode: '8517.12.00',
          currency: 'USD',
          totalCFValue: 150000,
          formMRegistrationDate: '2024-01-15',
          generalGoodsDescription: 'Mobile phones and accessories',
          paymentMode: 'Letter of Credit',
          fundApplicationId: 'FA-2024-001234',
        },
        rateInfo: {
          amount: 150000,
          rate: 1580.50,
          tolerance: 15000,
          calculatedValue: 165000,
        },
        documents: [],
        status: 'TRADE_APPROVED',
        createdAt: '2024-01-20T10:30:00Z',
        updatedAt: '2024-01-22T14:20:00Z',
        tradeTeamReview: {
          id: 'tr-001',
          reviewerId: '2',
          transactionReference: 'TXN-2024-001',
          status: 'APPROVED',
          comment: 'All documents verified and approved',
          reviewedAt: '2024-01-22T14:20:00Z',
        },
      },
      {
        id: 'BID-2024-002',
        rmId: user?.id || '1',
        accountInfo: {
          accountNumber: '**********',
          accountName: 'DEF Enterprises',
          bvn: '1**********',
          accountBalance: 1800000,
        },
        formMInfo: {
          refId: 'FM-2024-002',
          applicantName: 'DEF Enterprises',
          applicantAddress: '456 Commerce Street, Abuja',
          beneficiaryName: 'Global Suppliers Inc',
          applicantTin: '********-0002',
          rcNumber: 'RC654321',
          applicantPhone: '+234-************',
          countryOfSupply: 'Germany',
          hsCode: '8471.30.00',
          currency: 'EUR',
          totalCFValue: 85000,
          formMRegistrationDate: '2024-01-18',
          generalGoodsDescription: 'Computer equipment and software',
          paymentMode: 'Documentary Collection',
          fundApplicationId: 'FA-2024-002345',
        },
        rateInfo: {
          amount: 85000,
          rate: 1720.25,
          tolerance: 8500,
          calculatedValue: 93500,
        },
        documents: [],
        status: 'TRADE_REVIEW',
        createdAt: '2024-01-21T09:15:00Z',
        updatedAt: '2024-01-21T09:15:00Z',
      },
      {
        id: 'BID-2024-003',
        rmId: user?.id || '1',
        accountInfo: {
          accountNumber: '********55',
          accountName: 'GHI Manufacturing',
          bvn: '***********',
          accountBalance: 3200000,
        },
        formMInfo: {
          refId: 'FM-2024-003',
          applicantName: 'GHI Manufacturing',
          applicantAddress: '789 Industrial Area, Port Harcourt',
          beneficiaryName: 'Asian Tech Solutions',
          applicantTin: '********-0003',
          rcNumber: 'RC112233',
          applicantPhone: '+234-************',
          countryOfSupply: 'Japan',
          hsCode: '8479.89.00',
          currency: 'USD',
          totalCFValue: 220000,
          formMRegistrationDate: '2024-01-19',
          generalGoodsDescription: 'Industrial machinery and parts',
          paymentMode: 'Letter of Credit',
          fundApplicationId: 'FA-2024-003456',
        },
        rateInfo: {
          amount: 220000,
          rate: 1575.80,
          tolerance: 22000,
          calculatedValue: 242000,
        },
        documents: [],
        status: 'DRAFT',
        createdAt: '2024-01-22T16:45:00Z',
        updatedAt: '2024-01-22T16:45:00Z',
      },
    ];

    setTimeout(() => {
      setRequests(mockRequests);
      setLoading(false);
    }, 1000);
  }, [user?.id]);

  // Filter and search requests
  useEffect(() => {
    let filtered = [...requests];

    // Apply status filter
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(request => request.status === statusFilter);
    }

    // Apply search
    if (searchTerm) {
      filtered = searchItems(filtered, searchTerm, ['id', 'formMInfo.applicantName', 'formMInfo.refId']);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortConfig.key as keyof BidRequest];
      const bValue = b[sortConfig.key as keyof BidRequest];
      
      if (sortConfig.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredRequests(filtered);
    setCurrentPage(1);
  }, [requests, searchTerm, statusFilter, sortConfig]);

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    setSortConfig({ key, direction });
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    exportBidRequests(filteredRequests, format);
  };

  const handleViewRequest = (request: BidRequest) => {
    setSelectedRequest(request);
    setIsDetailsModalOpen(true);
  };

  const columns = [
    {
      key: 'id',
      header: 'Request ID',
      sortable: true,
    },
    {
      key: 'formMInfo.applicantName',
      header: 'Applicant',
      render: (request: BidRequest) => request.formMInfo.applicantName,
      sortable: true,
    },
    {
      key: 'formMInfo.currency',
      header: 'Amount',
      render: (request: BidRequest) => (
        <div>
          <div className="font-medium">
            {formatCurrency(request.rateInfo.amount, request.formMInfo.currency)}
          </div>
          <div className="text-xs text-gray-500">
            ≈ {formatCurrency(request.rateInfo.amount * request.rateInfo.rate, 'NGN')}
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      render: (request: BidRequest) => <StatusBadge status={request.status} />,
    },
    {
      key: 'createdAt',
      header: 'Created',
      render: (request: BidRequest) => formatDate(request.createdAt),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (request: BidRequest) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleViewRequest(request)}
            className="p-1 text-primary-600 hover:text-primary-800"
            title="View details"
          >
            <Eye className="w-4 h-4" />
          </button>
          {request.status === 'DRAFT' && (
            <Link
              to={`/requests/edit/${request.id}`}
              className="p-1 text-blue-600 hover:text-blue-800"
              title="Edit request"
            >
              <Edit className="w-4 h-4" />
            </Link>
          )}
        </div>
      ),
    },
  ];

  const totalPages = Math.ceil(filteredRequests.length / itemsPerPage);
  const paginatedRequests = filteredRequests.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Requests</h1>
          <p className="text-gray-600">Manage your bid confirmation requests</p>
        </div>
        <Link to="/requests/new" className="btn-primary flex items-center space-x-2">
          <Plus className="w-5 h-5" />
          <span>New Request</span>
        </Link>
      </div>

      {/* Filters and Search */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search requests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as BidStatus | 'ALL')}
              className="input-field"
            >
              <option value="ALL">All Status</option>
              <option value="DRAFT">Draft</option>
              <option value="SUBMITTED">Submitted</option>
              <option value="TRADE_REVIEW">Trade Review</option>
              <option value="TRADE_APPROVED">Trade Approved</option>
              <option value="TRADE_REJECTED">Trade Rejected</option>
              <option value="TREASURY_REVIEW">Treasury Review</option>
              <option value="FINALIZED">Finalized</option>
              <option value="COMPLETED">Completed</option>
              <option value="UNSUCCESSFUL">Unsuccessful</option>
            </select>
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Export</label>
            <div className="flex space-x-2">
              <button
                onClick={() => handleExport('excel')}
                className="btn-secondary flex items-center space-x-1 text-sm"
              >
                <Download className="w-4 h-4" />
                <span>Excel</span>
              </button>
              <button
                onClick={() => handleExport('pdf')}
                className="btn-secondary flex items-center space-x-1 text-sm"
              >
                <Download className="w-4 h-4" />
                <span>PDF</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Requests Table */}
      <Table
        data={paginatedRequests}
        columns={columns}
        loading={loading}
        emptyMessage="No requests found"
        pagination={{
          currentPage,
          totalPages,
          totalItems: filteredRequests.length,
          itemsPerPage,
          onPageChange: setCurrentPage,
        }}
        onSort={handleSort}
        sortConfig={sortConfig}
      />

      {/* Request Details Modal */}
      <RequestDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        request={selectedRequest}
      />
    </div>
  );
};

export default MyRequests;
