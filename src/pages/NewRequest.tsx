import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Save, Send, Eye } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import AccountInfoSection from '../components/requests/AccountInfoSection';
import FormMSection from '../components/requests/FormMSection';
import RateSection from '../components/requests/RateSection';
import DocumentUploadSection from '../components/requests/DocumentUploadSection';
import ReviewModal from '../components/requests/ReviewModal';
import type { BidRequest, AccountInfo, FormMInfo, RateInfo, DocumentUpload } from '../types';
import { calculate110Percent, calculateTolerance, generateId } from '../utils/helpers';

const validationSchema = Yup.object({
  accountInfo: Yup.object({
    accountNumber: Yup.string().required('Account number is required'),
    accountName: Yup.string().required('Account name is required'),
    bvn: Yup.string().required('BVN is required'),
    accountBalance: Yup.number().min(0, 'Account balance must be positive').required('Account balance is required'),
  }),
  formMInfo: Yup.object({
    refId: Yup.string().required('Form M Reference ID is required'),
    applicantName: Yup.string().required('Applicant name is required'),
    applicantAddress: Yup.string().required('Applicant address is required'),
    beneficiaryName: Yup.string().required('Beneficiary name is required'),
    applicantTin: Yup.string().required('TIN number is required'),
    rcNumber: Yup.string().required('RC number is required'),
    applicantPhone: Yup.string().required('Phone number is required'),
    countryOfSupply: Yup.string().required('Country of supply is required'),
    hsCode: Yup.string().required('HS Code is required'),
    currency: Yup.string().required('Currency is required'),
    totalCFValue: Yup.number().min(0, 'Total C&F Value must be positive').required('Total C&F Value is required'),
    formMRegistrationDate: Yup.string().required('Form M registration date is required'),
    generalGoodsDescription: Yup.string().required('Goods description is required'),
    paymentMode: Yup.string().required('Payment mode is required'),
    fundApplicationId: Yup.string().required('Fund application ID is required'),
  }),
  rateInfo: Yup.object({
    amount: Yup.number().min(0, 'Amount must be positive').required('Amount is required'),
    rate: Yup.number().min(0, 'Rate must be positive').required('Rate is required'),
  }),
});

const NewRequest: React.FC = () => {
  const navigate = useNavigate();
  const [documents, setDocuments] = useState<DocumentUpload[]>([]);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const formik = useFormik({
    initialValues: {
      accountInfo: {
        accountNumber: '',
        accountName: '',
        bvn: '',
        accountBalance: 0,
      } as AccountInfo,
      formMInfo: {
        refId: '',
        applicantName: '',
        applicantAddress: '',
        beneficiaryName: '',
        applicantTin: '',
        rcNumber: '',
        applicantPhone: '',
        countryOfSupply: '',
        hsCode: '',
        currency: 'USD',
        totalCFValue: 0,
        formMRegistrationDate: '',
        generalGoodsDescription: '',
        paymentMode: '',
        fundApplicationId: '',
      } as FormMInfo,
      rateInfo: {
        amount: 0,
        rate: 0,
        tolerance: 0,
        calculatedValue: 0,
      } as RateInfo,
    },
    validationSchema,
    onSubmit: async (values) => {
      // This would normally submit to an API
      console.log('Form submitted:', { ...values, documents });
      setIsSubmitting(true);
      
      // Simulate API call
      setTimeout(() => {
        setIsSubmitting(false);
        setIsReviewModalOpen(false);
        navigate('/requests');
      }, 2000);
    },
  });

  // Auto-calculate tolerance and 110% value when amount or totalCFValue changes
  React.useEffect(() => {
    const { amount } = formik.values.rateInfo;
    const { totalCFValue } = formik.values.formMInfo;
    
    if (amount > 0) {
      const tolerance = calculateTolerance(amount);
      formik.setFieldValue('rateInfo.tolerance', tolerance);
    }
    
    if (totalCFValue > 0) {
      const calculatedValue = calculate110Percent(totalCFValue);
      formik.setFieldValue('rateInfo.calculatedValue', calculatedValue);
    }
  }, [formik.values.rateInfo.amount, formik.values.formMInfo.totalCFValue]);

  const handleSaveDraft = () => {
    // Save as draft logic
    console.log('Saving as draft...');
  };

  const handlePreview = () => {
    setIsReviewModalOpen(true);
  };

  const handleDocumentsChange = (newDocuments: DocumentUpload[]) => {
    setDocuments(newDocuments);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">New Bid Request</h1>
          <p className="text-gray-600">Create a new bid confirmation request</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            type="button"
            onClick={handleSaveDraft}
            className="btn-secondary flex items-center space-x-2"
          >
            <Save className="w-5 h-5" />
            <span>Save Draft</span>
          </button>
          
          <button
            type="button"
            onClick={handlePreview}
            disabled={!formik.isValid}
            className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Eye className="w-5 h-5" />
            <span>Preview & Submit</span>
          </button>
        </div>
      </div>

      <form onSubmit={formik.handleSubmit} className="space-y-8">
        {/* Account Information Section */}
        <AccountInfoSection
          values={formik.values.accountInfo}
          errors={formik.errors.accountInfo}
          touched={formik.touched.accountInfo}
          setFieldValue={formik.setFieldValue}
          setFieldTouched={formik.setFieldTouched}
        />

        {/* Form M Section */}
        <FormMSection
          values={formik.values.formMInfo}
          errors={formik.errors.formMInfo}
          touched={formik.touched.formMInfo}
          setFieldValue={formik.setFieldValue}
          setFieldTouched={formik.setFieldTouched}
        />

        {/* Rate Section */}
        <RateSection
          values={formik.values.rateInfo}
          errors={formik.errors.rateInfo}
          touched={formik.touched.rateInfo}
          setFieldValue={formik.setFieldValue}
          setFieldTouched={formik.setFieldTouched}
        />

        {/* Document Upload Section */}
        <DocumentUploadSection
          documents={documents}
          onDocumentsChange={handleDocumentsChange}
        />
      </form>

      {/* Review Modal */}
      <ReviewModal
        isOpen={isReviewModalOpen}
        onClose={() => setIsReviewModalOpen(false)}
        onSubmit={formik.handleSubmit}
        formData={{
          accountInfo: formik.values.accountInfo,
          formMInfo: formik.values.formMInfo,
          rateInfo: formik.values.rateInfo,
          documents,
        }}
        isSubmitting={isSubmitting}
      />
    </div>
  );
};

export default NewRequest;
