import React, { useState, useEffect } from 'react';
import { Search, Download, Filter, Calendar, User, Activity } from 'lucide-react';
import Table from '../components/common/Table';
import type { AuditLog } from '../types';
import { exportAuditLogs } from '../utils/export';
import { formatDateTime, searchItems, filterByDateRange } from '../utils/helpers';

const AuditLogs: React.FC = () => {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState<string>('ALL');
  const [userFilter, setUserFilter] = useState<string>('ALL');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' }>({
    key: 'timestamp',
    direction: 'desc',
  });

  const itemsPerPage = 15;

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockLogs: AuditLog[] = [
      {
        id: 'log-001',
        userId: '1',
        userName: 'John Doe (RM)',
        action: 'LOGIN',
        resource: 'Authentication',
        details: 'User logged in successfully',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp: '2024-01-24T08:30:00Z',
      },
      {
        id: 'log-002',
        userId: '1',
        userName: 'John Doe (RM)',
        action: 'CREATE',
        resource: 'Bid Request',
        details: 'Created new bid request BID-2024-007',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp: '2024-01-24T09:15:00Z',
      },
      {
        id: 'log-003',
        userId: '2',
        userName: 'Jane Smith (Trade Team)',
        action: 'LOGIN',
        resource: 'Authentication',
        details: 'User logged in successfully',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        timestamp: '2024-01-24T09:45:00Z',
      },
      {
        id: 'log-004',
        userId: '2',
        userName: 'Jane Smith (Trade Team)',
        action: 'APPROVE',
        resource: 'Bid Request',
        details: 'Approved bid request BID-2024-005 with transaction reference TXN-2024-005',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        timestamp: '2024-01-24T10:20:00Z',
      },
      {
        id: 'log-005',
        userId: '3',
        userName: 'Mike Johnson (Treasury)',
        action: 'LOGIN',
        resource: 'Authentication',
        details: 'User logged in successfully',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp: '2024-01-24T11:00:00Z',
      },
      {
        id: 'log-006',
        userId: '3',
        userName: 'Mike Johnson (Treasury)',
        action: 'FINALIZE',
        resource: 'Bid Request',
        details: 'Finalized bid request BID-2024-003 as successful',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp: '2024-01-24T11:30:00Z',
      },
      {
        id: 'log-007',
        userId: '4',
        userName: 'Sarah Wilson (SGC)',
        action: 'LOGIN',
        resource: 'Authentication',
        details: 'User logged in successfully',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp: '2024-01-24T12:00:00Z',
      },
      {
        id: 'log-008',
        userId: '4',
        userName: 'Sarah Wilson (SGC)',
        action: 'CREATE',
        resource: 'User Management',
        details: 'Created new user <NAME_EMAIL>',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp: '2024-01-24T12:15:00Z',
      },
      {
        id: 'log-009',
        userId: '1',
        userName: 'John Doe (RM)',
        action: 'UPDATE',
        resource: 'Bid Request',
        details: 'Updated bid request BID-2024-007 with additional documents',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp: '2024-01-24T13:45:00Z',
      },
      {
        id: 'log-010',
        userId: '2',
        userName: 'Jane Smith (Trade Team)',
        action: 'REJECT',
        resource: 'Bid Request',
        details: 'Rejected bid request BID-2024-008 due to incomplete documentation',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        timestamp: '2024-01-24T14:30:00Z',
      },
      {
        id: 'log-011',
        userId: '5',
        userName: 'David Brown (Auditor)',
        action: 'LOGIN',
        resource: 'Authentication',
        details: 'User logged in successfully',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp: '2024-01-24T15:00:00Z',
      },
      {
        id: 'log-012',
        userId: '5',
        userName: 'David Brown (Auditor)',
        action: 'EXPORT',
        resource: 'Audit Logs',
        details: 'Exported audit logs for date range 2024-01-01 to 2024-01-24',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp: '2024-01-24T15:15:00Z',
      },
    ];

    setTimeout(() => {
      setLogs(mockLogs);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and search logs
  useEffect(() => {
    let filtered = [...logs];

    // Apply action filter
    if (actionFilter !== 'ALL') {
      filtered = filtered.filter(log => log.action === actionFilter);
    }

    // Apply user filter
    if (userFilter !== 'ALL') {
      filtered = filtered.filter(log => log.userId === userFilter);
    }

    // Apply date range filter
    if (dateFrom || dateTo) {
      filtered = filterByDateRange(filtered, dateFrom, dateTo);
    }

    // Apply search
    if (searchTerm) {
      filtered = searchItems(filtered, searchTerm, ['userName', 'action', 'resource', 'details', 'ipAddress']);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortConfig.key as keyof AuditLog];
      const bValue = b[sortConfig.key as keyof AuditLog];
      
      if (sortConfig.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredLogs(filtered);
    setCurrentPage(1);
  }, [logs, searchTerm, actionFilter, userFilter, dateFrom, dateTo, sortConfig]);

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    setSortConfig({ key, direction });
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    exportAuditLogs(filteredLogs, format);
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'LOGIN':
        return <User className="w-4 h-4 text-blue-600" />;
      case 'CREATE':
        return <span className="w-4 h-4 text-green-600">+</span>;
      case 'UPDATE':
        return <span className="w-4 h-4 text-yellow-600">✎</span>;
      case 'DELETE':
        return <span className="w-4 h-4 text-red-600">×</span>;
      case 'APPROVE':
        return <span className="w-4 h-4 text-green-600">✓</span>;
      case 'REJECT':
        return <span className="w-4 h-4 text-red-600">✗</span>;
      case 'FINALIZE':
        return <span className="w-4 h-4 text-purple-600">⚡</span>;
      case 'EXPORT':
        return <Download className="w-4 h-4 text-gray-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'LOGIN':
        return 'bg-blue-100 text-blue-800';
      case 'CREATE':
        return 'bg-green-100 text-green-800';
      case 'UPDATE':
        return 'bg-yellow-100 text-yellow-800';
      case 'DELETE':
        return 'bg-red-100 text-red-800';
      case 'APPROVE':
        return 'bg-green-100 text-green-800';
      case 'REJECT':
        return 'bg-red-100 text-red-800';
      case 'FINALIZE':
        return 'bg-purple-100 text-purple-800';
      case 'EXPORT':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      key: 'timestamp',
      header: 'Timestamp',
      render: (log: AuditLog) => (
        <div className="text-sm">
          {formatDateTime(log.timestamp)}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'userName',
      header: 'User',
      render: (log: AuditLog) => (
        <div className="text-sm font-medium">
          {log.userName}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'action',
      header: 'Action',
      render: (log: AuditLog) => (
        <div className="flex items-center space-x-2">
          {getActionIcon(log.action)}
          <span className={`status-badge ${getActionColor(log.action)}`}>
            {log.action}
          </span>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'resource',
      header: 'Resource',
      render: (log: AuditLog) => (
        <div className="text-sm text-gray-900">
          {log.resource}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'details',
      header: 'Details',
      render: (log: AuditLog) => (
        <div className="text-sm text-gray-600 max-w-xs truncate" title={log.details}>
          {log.details}
        </div>
      ),
    },
    {
      key: 'ipAddress',
      header: 'IP Address',
      render: (log: AuditLog) => (
        <div className="text-sm font-mono text-gray-600">
          {log.ipAddress}
        </div>
      ),
    },
  ];

  const totalPages = Math.ceil(filteredLogs.length / itemsPerPage);
  const paginatedLogs = filteredLogs.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const uniqueActions = [...new Set(logs.map(log => log.action))];
  const uniqueUsers = [...new Set(logs.map(log => ({ id: log.userId, name: log.userName })))];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Audit Logs</h1>
          <p className="text-gray-600">Monitor user activities and system events</p>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Action</label>
            <select
              value={actionFilter}
              onChange={(e) => setActionFilter(e.target.value)}
              className="input-field"
            >
              <option value="ALL">All Actions</option>
              {uniqueActions.map((action) => (
                <option key={action} value={action}>
                  {action}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">User</label>
            <select
              value={userFilter}
              onChange={(e) => setUserFilter(e.target.value)}
              className="input-field"
            >
              <option value="ALL">All Users</option>
              {uniqueUsers.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
            <input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              className="input-field"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">To Date</label>
            <input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              className="input-field"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Export</label>
            <div className="flex space-x-2">
              <button
                onClick={() => handleExport('excel')}
                className="btn-secondary flex items-center space-x-1 text-sm"
              >
                <Download className="w-4 h-4" />
                <span>Excel</span>
              </button>
              <button
                onClick={() => handleExport('pdf')}
                className="btn-secondary flex items-center space-x-1 text-sm"
              >
                <Download className="w-4 h-4" />
                <span>PDF</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card p-4">
          <div className="flex items-center">
            <div className="p-2 bg-primary-100 rounded-lg">
              <Activity className="w-6 h-6 text-primary-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Activities</p>
              <p className="text-2xl font-bold text-gray-900">{filteredLogs.length}</p>
            </div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <User className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Users</p>
              <p className="text-2xl font-bold text-gray-900">{uniqueUsers.length}</p>
            </div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <span className="w-6 h-6 text-green-600 flex items-center justify-center font-bold">✓</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Approvals</p>
              <p className="text-2xl font-bold text-gray-900">
                {filteredLogs.filter(log => log.action === 'APPROVE').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <span className="w-6 h-6 text-red-600 flex items-center justify-center font-bold">✗</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Rejections</p>
              <p className="text-2xl font-bold text-gray-900">
                {filteredLogs.filter(log => log.action === 'REJECT').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Audit Logs Table */}
      <Table
        data={paginatedLogs}
        columns={columns}
        loading={loading}
        emptyMessage="No audit logs found"
        pagination={{
          currentPage,
          totalPages,
          onPageChange: setCurrentPage,
        }}
        onSort={handleSort}
        sortConfig={sortConfig}
      />
    </div>
  );
};

export default AuditLogs;
