import React, { useState, useEffect } from 'react';
import { Search, Download, Eye, Filter, TrendingUp, Clock, CheckCircle, XCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Table from '../components/common/Table';
import StatusBadge from '../components/common/StatusBadge';
import type { BidRequest, BidStatus } from '../types';
import { exportBidRequests } from '../utils/export';
import { formatCurrency, formatDate, searchItems, getRoleDisplayName } from '../utils/helpers';
import { useAuth } from '../contexts/AuthContext';

const AllRequests: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [requests, setRequests] = useState<BidRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<BidRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<BidStatus | 'ALL'>('ALL');
  const [currencyFilter, setCurrencyFilter] = useState<string>('ALL');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' }>({
    key: 'createdAt',
    direction: 'desc',
  });

  const itemsPerPage = 10;

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockRequests: BidRequest[] = [
      {
        id: 'BID-2024-001',
        rmId: '1',
        accountInfo: {
          accountNumber: '********90',
          accountName: 'ABC Trading Ltd',
          bvn: '***********',
          accountBalance: 2500000,
        },
        formMInfo: {
          refId: 'FM-2024-001',
          applicantName: 'ABC Trading Ltd',
          applicantAddress: '123 Business District, Lagos',
          beneficiaryName: 'XYZ International',
          applicantTin: '********-0001',
          rcNumber: 'RC123456',
          applicantPhone: '+234-************',
          countryOfSupply: 'China',
          hsCode: '8517.12.00',
          currency: 'USD',
          totalCFValue: 150000,
          formMRegistrationDate: '2024-01-15',
          generalGoodsDescription: 'Mobile phones and accessories',
          paymentMode: 'Letter of Credit',
          fundApplicationId: 'FA-2024-001234',
        },
        rateInfo: {
          amount: 150000,
          rate: 1580.50,
          tolerance: 15000,
          calculatedValue: 165000,
        },
        documents: [],
        status: 'COMPLETED',
        createdAt: '2024-01-20T10:30:00Z',
        updatedAt: '2024-01-25T16:20:00Z',
        tradeTeamReview: {
          id: 'tr-001',
          reviewerId: '2',
          transactionReference: 'TXN-2024-001',
          status: 'APPROVED',
          comment: 'All documents verified and approved',
          reviewedAt: '2024-01-22T14:20:00Z',
        },
        treasuryReview: {
          id: 'treasury-001',
          reviewerId: '3',
          isSuccessful: true,
          finalizedAt: '2024-01-25T16:20:00Z',
          comment: 'Successfully processed',
        },
      },
      {
        id: 'BID-2024-002',
        rmId: '1',
        accountInfo: {
          accountNumber: '**********',
          accountName: 'DEF Enterprises',
          bvn: '1**********',
          accountBalance: 1800000,
        },
        formMInfo: {
          refId: 'FM-2024-002',
          applicantName: 'DEF Enterprises',
          applicantAddress: '456 Commerce Street, Abuja',
          beneficiaryName: 'Global Suppliers Inc',
          applicantTin: '********-0002',
          rcNumber: 'RC654321',
          applicantPhone: '+234-************',
          countryOfSupply: 'Germany',
          hsCode: '8471.30.00',
          currency: 'EUR',
          totalCFValue: 85000,
          formMRegistrationDate: '2024-01-18',
          generalGoodsDescription: 'Computer equipment and software',
          paymentMode: 'Documentary Collection',
          fundApplicationId: 'FA-2024-002345',
        },
        rateInfo: {
          amount: 85000,
          rate: 1720.25,
          tolerance: 8500,
          calculatedValue: 93500,
        },
        documents: [],
        status: 'TRADE_REVIEW',
        createdAt: '2024-01-21T09:15:00Z',
        updatedAt: '2024-01-21T09:15:00Z',
      },
      {
        id: 'BID-2024-003',
        rmId: '1',
        accountInfo: {
          accountNumber: '**********',
          accountName: 'GHI Manufacturing',
          bvn: '**********6',
          accountBalance: 3200000,
        },
        formMInfo: {
          refId: 'FM-2024-003',
          applicantName: 'GHI Manufacturing',
          applicantAddress: '789 Industrial Area, Port Harcourt',
          beneficiaryName: 'Asian Tech Solutions',
          applicantTin: '********-0003',
          rcNumber: 'RC112233',
          applicantPhone: '+234-************',
          countryOfSupply: 'Japan',
          hsCode: '8479.89.00',
          currency: 'USD',
          totalCFValue: 220000,
          formMRegistrationDate: '2024-01-19',
          generalGoodsDescription: 'Industrial machinery and parts',
          paymentMode: 'Letter of Credit',
          fundApplicationId: 'FA-2024-003456',
        },
        rateInfo: {
          amount: 220000,
          rate: 1575.80,
          tolerance: 22000,
          calculatedValue: 242000,
        },
        documents: [],
        status: 'TRADE_REJECTED',
        createdAt: '2024-01-22T16:45:00Z',
        updatedAt: '2024-01-23T10:30:00Z',
        tradeTeamReview: {
          id: 'tr-003',
          reviewerId: '2',
          transactionReference: '',
          status: 'REJECTED',
          comment: 'Incomplete documentation provided',
          reviewedAt: '2024-01-23T10:30:00Z',
        },
      },
      {
        id: 'BID-2024-004',
        rmId: '1',
        accountInfo: {
          accountNumber: '22********',
          accountName: 'JKL Import Export',
          bvn: '22********7',
          accountBalance: 4500000,
        },
        formMInfo: {
          refId: 'FM-2024-004',
          applicantName: 'JKL Import Export',
          applicantAddress: '321 Trade Center, Lagos',
          beneficiaryName: 'European Suppliers Ltd',
          applicantTin: '********-0004',
          rcNumber: 'RC223344',
          applicantPhone: '+234-************',
          countryOfSupply: 'Italy',
          hsCode: '6204.62.00',
          currency: 'EUR',
          totalCFValue: 95000,
          formMRegistrationDate: '2024-01-20',
          generalGoodsDescription: 'Textile and clothing materials',
          paymentMode: 'Letter of Credit',
          fundApplicationId: 'FA-2024-004567',
        },
        rateInfo: {
          amount: 95000,
          rate: 1720.25,
          tolerance: 9500,
          calculatedValue: 104500,
        },
        documents: [],
        status: 'TREASURY_REVIEW',
        createdAt: '2024-01-23T08:30:00Z',
        updatedAt: '2024-01-24T11:45:00Z',
        tradeTeamReview: {
          id: 'tr-004',
          reviewerId: '2',
          transactionReference: 'TXN-2024-004',
          status: 'APPROVED',
          comment: 'Documentation verified and approved',
          reviewedAt: '2024-01-24T11:45:00Z',
        },
      },
      {
        id: 'BID-2024-005',
        rmId: '1',
        accountInfo: {
          accountNumber: '********77',
          accountName: 'MNO Technologies',
          bvn: '********778',
          accountBalance: 6200000,
        },
        formMInfo: {
          refId: 'FM-2024-005',
          applicantName: 'MNO Technologies',
          applicantAddress: '654 Tech Hub, Abuja',
          beneficiaryName: 'Silicon Valley Innovations',
          applicantTin: '********-0005',
          rcNumber: 'RC334455',
          applicantPhone: '+234-************',
          countryOfSupply: 'USA',
          hsCode: '8471.70.00',
          currency: 'USD',
          totalCFValue: 180000,
          formMRegistrationDate: '2024-01-21',
          generalGoodsDescription: 'Computer servers and networking equipment',
          paymentMode: 'Documentary Collection',
          fundApplicationId: 'FA-2024-005678',
        },
        rateInfo: {
          amount: 180000,
          rate: 1580.50,
          tolerance: 18000,
          calculatedValue: 198000,
        },
        documents: [],
        status: 'UNSUCCESSFUL',
        createdAt: '2024-01-23T11:15:00Z',
        updatedAt: '2024-01-25T14:30:00Z',
        tradeTeamReview: {
          id: 'tr-005',
          reviewerId: '2',
          transactionReference: 'TXN-2024-005',
          status: 'APPROVED',
          comment: 'Trade approval completed',
          reviewedAt: '2024-01-24T09:20:00Z',
        },
        treasuryReview: {
          id: 'treasury-005',
          reviewerId: '3',
          isSuccessful: false,
          finalizedAt: '2024-01-25T14:30:00Z',
          comment: 'Payment processing failed',
        },
      },
    ];

    setTimeout(() => {
      setRequests(mockRequests);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and search requests
  useEffect(() => {
    let filtered = [...requests];

    // Apply status filter
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(request => request.status === statusFilter);
    }

    // Apply currency filter
    if (currencyFilter !== 'ALL') {
      filtered = filtered.filter(request => request.formMInfo.currency === currencyFilter);
    }

    // Apply date range filter
    if (dateFrom || dateTo) {
      filtered = filtered.filter(request => {
        const requestDate = new Date(request.createdAt);
        const start = dateFrom ? new Date(dateFrom) : null;
        const end = dateTo ? new Date(dateTo) : null;
        
        if (start && requestDate < start) return false;
        if (end && requestDate > end) return false;
        return true;
      });
    }

    // Apply search
    if (searchTerm) {
      filtered = searchItems(filtered, searchTerm, ['id', 'formMInfo.applicantName', 'formMInfo.refId', 'accountInfo.accountNumber']);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortConfig.key as keyof BidRequest];
      const bValue = b[sortConfig.key as keyof BidRequest];
      
      if (sortConfig.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredRequests(filtered);
    setCurrentPage(1);
  }, [requests, searchTerm, statusFilter, currencyFilter, dateFrom, dateTo, sortConfig]);

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    setSortConfig({ key, direction });
  };

  const handleExport = (format: 'excel' | 'pdf') => {
    exportBidRequests(filteredRequests, format);
  };

  const columns = [
    {
      key: 'id',
      header: 'Request ID',
      sortable: true,
    },
    {
      key: 'formMInfo.applicantName',
      header: 'Applicant',
      render: (request: BidRequest) => (
        <div>
          <div className="font-medium">{request.formMInfo.applicantName}</div>
          <div className="text-xs text-gray-500">{request.formMInfo.refId}</div>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'accountInfo.accountNumber',
      header: 'Account',
      render: (request: BidRequest) => (
        <div>
          <div className="font-medium">{request.accountInfo.accountNumber}</div>
          <div className="text-xs text-gray-500">{request.accountInfo.accountName}</div>
        </div>
      ),
    },
    {
      key: 'formMInfo.currency',
      header: 'Amount',
      render: (request: BidRequest) => (
        <div>
          <div className="font-medium">
            {formatCurrency(request.rateInfo.amount, request.formMInfo.currency)}
          </div>
          <div className="text-xs text-gray-500">
            ≈ {formatCurrency(request.rateInfo.amount * request.rateInfo.rate, 'NGN')}
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      render: (request: BidRequest) => <StatusBadge status={request.status} />,
    },
    {
      key: 'tradeTeamReview.transactionReference',
      header: 'Trade Ref',
      render: (request: BidRequest) => (
        <div className="text-sm font-mono">
          {request.tradeTeamReview?.transactionReference || '-'}
        </div>
      ),
    },
    {
      key: 'createdAt',
      header: 'Created',
      render: (request: BidRequest) => formatDate(request.createdAt),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (request: BidRequest) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => navigate(`/requests/${request.id}`)}
            className="p-1 text-primary-600 hover:text-primary-800"
            title="View details"
          >
            <Eye className="w-4 h-4" />
          </button>
        </div>
      ),
    },
  ];

  const totalPages = Math.ceil(filteredRequests.length / itemsPerPage);
  const paginatedRequests = filteredRequests.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const uniqueCurrencies = [...new Set(requests.map(request => request.formMInfo.currency))];

  // Calculate stats
  const stats = {
    total: requests.length,
    pending: requests.filter(r => ['SUBMITTED', 'TRADE_REVIEW', 'TREASURY_REVIEW'].includes(r.status)).length,
    completed: requests.filter(r => r.status === 'COMPLETED').length,
    rejected: requests.filter(r => ['TRADE_REJECTED', 'UNSUCCESSFUL'].includes(r.status)).length,
    totalValue: requests.reduce((sum, r) => sum + (r.rateInfo.amount * r.rateInfo.rate), 0),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">All Requests</h1>
          <p className="text-gray-600">
            {user?.role === 'SGC' ? 'System overview of all bid confirmation requests' : 'Audit view of all bid confirmation requests'}
          </p>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="card p-4">
          <div className="flex items-center">
            <div className="p-2 bg-primary-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-primary-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Requests</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="flex items-center">
            <div className="p-2 bg-warning-100 rounded-lg">
              <Clock className="w-6 h-6 text-warning-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
            </div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="flex items-center">
            <div className="p-2 bg-success-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-success-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">{stats.completed}</p>
            </div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="flex items-center">
            <div className="p-2 bg-danger-100 rounded-lg">
              <XCircle className="w-6 h-6 text-danger-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Rejected</p>
              <p className="text-2xl font-bold text-gray-900">{stats.rejected}</p>
            </div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="flex items-center">
            <div className="p-2 bg-wine-100 rounded-lg">
              <span className="w-6 h-6 text-wine-600 flex items-center justify-center font-bold">₦</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Value</p>
              <p className="text-2xl font-bold text-gray-900">₦{(stats.totalValue / 1000000).toFixed(1)}M</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search requests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as BidStatus | 'ALL')}
              className="input-field"
            >
              <option value="ALL">All Status</option>
              <option value="DRAFT">Draft</option>
              <option value="SUBMITTED">Submitted</option>
              <option value="TRADE_REVIEW">Trade Review</option>
              <option value="TRADE_APPROVED">Trade Approved</option>
              <option value="TRADE_REJECTED">Trade Rejected</option>
              <option value="TREASURY_REVIEW">Treasury Review</option>
              <option value="FINALIZED">Finalized</option>
              <option value="COMPLETED">Completed</option>
              <option value="UNSUCCESSFUL">Unsuccessful</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
            <select
              value={currencyFilter}
              onChange={(e) => setCurrencyFilter(e.target.value)}
              className="input-field"
            >
              <option value="ALL">All Currencies</option>
              {uniqueCurrencies.map((currency) => (
                <option key={currency} value={currency}>
                  {currency}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
            <input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              className="input-field"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">To Date</label>
            <input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              className="input-field"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Export</label>
            <div className="flex space-x-2">
              <button
                onClick={() => handleExport('excel')}
                className="btn-secondary flex items-center space-x-1 text-sm"
              >
                <Download className="w-4 h-4" />
                <span>Excel</span>
              </button>
              <button
                onClick={() => handleExport('pdf')}
                className="btn-secondary flex items-center space-x-1 text-sm"
              >
                <Download className="w-4 h-4" />
                <span>PDF</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Requests Table */}
      <Table
        data={paginatedRequests}
        columns={columns}
        loading={loading}
        emptyMessage="No requests found"
        pagination={{
          currentPage,
          totalPages,
          totalItems: filteredRequests.length,
          itemsPerPage,
          onPageChange: setCurrentPage,
        }}
        onSort={handleSort}
        sortConfig={sortConfig}
      />
    </div>
  );
};

export default AllRequests;
