import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  FileText, 
  DollarSign, 
  CreditCard, 
  Upload, 
  Clock, 
  CheckCircle, 
  XCircle,
  Edit,
  MessageSquare,
  Download
} from 'lucide-react';
import StatusBadge from '../components/common/StatusBadge';
import ConfirmDialog from '../components/common/ConfirmDialog';
import Modal from '../components/common/Modal';
import type { BidRequest } from '../types';
import { formatCurrency, formatDate, formatDateTime, getFileSize } from '../utils/helpers';
import { useAuth } from '../contexts/AuthContext';

const RequestDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [request, setRequest] = useState<BidRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [isActionModalOpen, setIsActionModalOpen] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject' | 'finalize'>('approve');
  const [comment, setComment] = useState('');
  const [transactionRef, setTransactionRef] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mock data fetch - replace with actual API call
  useEffect(() => {
    const fetchRequest = async () => {
      setLoading(true);
      
      // Mock API call
      setTimeout(() => {
        const mockRequest: BidRequest = {
          id: id || 'BID-2024-001',
          rmId: '1',
          accountInfo: {
            accountNumber: '********90',
            accountName: 'ABC Trading Ltd',
            bvn: '***********',
            accountBalance: 2500000,
          },
          formMInfo: {
            refId: 'FM-2024-001',
            applicantName: 'ABC Trading Ltd',
            applicantAddress: '123 Business District, Lagos, Nigeria',
            beneficiaryName: 'XYZ International Suppliers',
            applicantTin: '********-0001',
            rcNumber: 'RC123456',
            applicantPhone: '+234-************',
            countryOfSupply: 'China',
            hsCode: '8517.12.00',
            currency: 'USD',
            totalCFValue: 150000,
            formMRegistrationDate: '2024-01-15',
            generalGoodsDescription: 'Mobile phones and accessories for retail distribution',
            paymentMode: 'Letter of Credit',
            fundApplicationId: 'FA-2024-001234',
          },
          rateInfo: {
            amount: 150000,
            rate: 1580.50,
            tolerance: 15000,
            calculatedValue: 165000,
          },
          documents: [
            {
              id: 'doc-1',
              fileName: 'form-m-certificate.pdf',
              fileSize: 2048576,
              fileType: 'application/pdf',
              uploadedAt: '2024-01-20T10:30:00Z',
              url: '#',
            },
            {
              id: 'doc-2',
              fileName: 'proforma-invoice.pdf',
              fileSize: 1536000,
              fileType: 'application/pdf',
              uploadedAt: '2024-01-20T10:32:00Z',
              url: '#',
            },
          ],
          status: 'TRADE_REVIEW',
          createdAt: '2024-01-20T10:30:00Z',
          updatedAt: '2024-01-22T14:20:00Z',
        };
        
        setRequest(mockRequest);
        setLoading(false);
      }, 1000);
    };

    fetchRequest();
  }, [id]);

  const canPerformAction = () => {
    if (!request || !user) return false;
    
    switch (user.role) {
      case 'TRADE_TEAM':
        return ['SUBMITTED', 'TRADE_REVIEW'].includes(request.status);
      case 'TREASURY':
        return ['TRADE_APPROVED', 'TREASURY_REVIEW'].includes(request.status);
      case 'RM':
        return request.status === 'DRAFT' && request.rmId === user.id;
      default:
        return false;
    }
  };

  const getAvailableActions = () => {
    if (!request || !user) return [];
    
    const actions = [];
    
    switch (user.role) {
      case 'TRADE_TEAM':
        if (['SUBMITTED', 'TRADE_REVIEW'].includes(request.status)) {
          actions.push(
            { type: 'approve', label: 'Approve Request', icon: CheckCircle, color: 'success' },
            { type: 'reject', label: 'Reject Request', icon: XCircle, color: 'danger' }
          );
        }
        break;
      case 'TREASURY':
        if (['TRADE_APPROVED', 'TREASURY_REVIEW'].includes(request.status)) {
          actions.push(
            { type: 'finalize', label: 'Finalize Request', icon: CheckCircle, color: 'success' }
          );
        }
        break;
      case 'RM':
        if (request.status === 'DRAFT' && request.rmId === user.id) {
          actions.push(
            { type: 'edit', label: 'Edit Request', icon: Edit, color: 'primary' }
          );
        }
        break;
    }
    
    return actions;
  };

  const handleAction = (type: string) => {
    if (type === 'edit') {
      navigate(`/requests/edit/${request?.id}`);
      return;
    }
    
    setActionType(type as 'approve' | 'reject' | 'finalize');
    setComment('');
    setTransactionRef('');
    setIsSuccessful(true);
    setIsActionModalOpen(true);
  };

  const handleSubmitAction = () => {
    setIsActionModalOpen(false);
    setIsConfirmDialogOpen(true);
  };

  const handleConfirmAction = async () => {
    if (!request) return;
    
    setIsSubmitting(true);
    
    // Mock API call
    setTimeout(() => {
      // Update request status based on action
      let newStatus = request.status;
      
      switch (actionType) {
        case 'approve':
          newStatus = 'TRADE_APPROVED';
          break;
        case 'reject':
          newStatus = 'TRADE_REJECTED';
          break;
        case 'finalize':
          newStatus = isSuccessful ? 'COMPLETED' : 'UNSUCCESSFUL';
          break;
      }
      
      setRequest({
        ...request,
        status: newStatus,
        updatedAt: new Date().toISOString(),
      });
      
      setIsSubmitting(false);
      setIsConfirmDialogOpen(false);
    }, 2000);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="spinner w-8 h-8"></div>
      </div>
    );
  }

  if (!request) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900">Request not found</h2>
        <p className="text-gray-600 mt-2">The requested bid confirmation could not be found.</p>
        <button
          onClick={() => navigate(-1)}
          className="btn-primary mt-4"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate(-1)}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{request.id}</h1>
            <p className="text-gray-600">Request Details</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <StatusBadge status={request.status} />
          {getAvailableActions().map((action) => {
            const Icon = action.icon;
            return (
              <button
                key={action.type}
                onClick={() => handleAction(action.type)}
                className={`btn-${action.color} flex items-center space-x-2`}
              >
                <Icon className="w-4 h-4" />
                <span>{action.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Status and Basic Info */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Created</h3>
            <p className="text-lg font-semibold text-gray-900">{formatDate(request.createdAt)}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Last Updated</h3>
            <p className="text-lg font-semibold text-gray-900">{formatDate(request.updatedAt)}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Total Value</h3>
            <p className="text-lg font-semibold text-primary-600">
              {formatCurrency(request.rateInfo.amount * request.rateInfo.rate, 'NGN')}
            </p>
          </div>
        </div>
      </div>

      {/* Account Information */}
      <div className="card p-6">
        <div className="flex items-center space-x-2 mb-4">
          <CreditCard className="w-5 h-5 text-primary-600" />
          <h2 className="text-lg font-semibold text-gray-900">Account Information</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span className="text-sm font-medium text-gray-500">Account Number</span>
            <p className="text-gray-900 font-mono">{request.accountInfo.accountNumber}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Account Name</span>
            <p className="text-gray-900">{request.accountInfo.accountName}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">BVN</span>
            <p className="text-gray-900 font-mono">{request.accountInfo.bvn}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Account Balance</span>
            <p className="text-gray-900 font-semibold">{formatCurrency(request.accountInfo.accountBalance, 'NGN')}</p>
          </div>
        </div>
      </div>

      {/* Form M Information */}
      <div className="card p-6">
        <div className="flex items-center space-x-2 mb-4">
          <FileText className="w-5 h-5 text-primary-600" />
          <h2 className="text-lg font-semibold text-gray-900">Form M Information</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span className="text-sm font-medium text-gray-500">Reference ID</span>
            <p className="text-gray-900 font-mono">{request.formMInfo.refId}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Applicant Name</span>
            <p className="text-gray-900">{request.formMInfo.applicantName}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Beneficiary Name</span>
            <p className="text-gray-900">{request.formMInfo.beneficiaryName}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Country of Supply</span>
            <p className="text-gray-900">{request.formMInfo.countryOfSupply}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Currency</span>
            <p className="text-gray-900 font-semibold">{request.formMInfo.currency}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Total C&F Value</span>
            <p className="text-gray-900 font-semibold">{formatCurrency(request.formMInfo.totalCFValue, request.formMInfo.currency)}</p>
          </div>
          <div className="md:col-span-2">
            <span className="text-sm font-medium text-gray-500">Goods Description</span>
            <p className="text-gray-900">{request.formMInfo.generalGoodsDescription}</p>
          </div>
        </div>
      </div>

      {/* Rate Information */}
      <div className="card p-6">
        <div className="flex items-center space-x-2 mb-4">
          <DollarSign className="w-5 h-5 text-primary-600" />
          <h2 className="text-lg font-semibold text-gray-900">Rate Information</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span className="text-sm font-medium text-gray-500">Amount</span>
            <p className="text-gray-900 font-semibold">{formatCurrency(request.rateInfo.amount, request.formMInfo.currency)}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Exchange Rate</span>
            <p className="text-gray-900 font-mono">{request.rateInfo.rate.toFixed(4)}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Tolerance (10%)</span>
            <p className="text-gray-900">{formatCurrency(request.rateInfo.tolerance, request.formMInfo.currency)}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Naira Equivalent</span>
            <p className="text-gray-900 text-lg font-bold text-primary-600">
              {formatCurrency(request.rateInfo.amount * request.rateInfo.rate, 'NGN')}
            </p>
          </div>
        </div>
      </div>

      {/* Documents */}
      <div className="card p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Upload className="w-5 h-5 text-primary-600" />
          <h2 className="text-lg font-semibold text-gray-900">
            Documents ({request.documents.length})
          </h2>
        </div>
        {request.documents.length > 0 ? (
          <div className="space-y-3">
            {request.documents.map((document) => (
              <div key={document.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <FileText className="w-5 h-5 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{document.fileName}</p>
                    <p className="text-xs text-gray-500">{getFileSize(document.fileSize)} • {formatDateTime(document.uploadedAt)}</p>
                  </div>
                </div>
                <button className="p-1 text-primary-600 hover:text-primary-800">
                  <Download className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No documents uploaded</p>
        )}
      </div>

      {/* Timeline */}
      <div className="card p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Clock className="w-5 h-5 text-primary-600" />
          <h2 className="text-lg font-semibold text-gray-900">Timeline</h2>
        </div>
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-primary-500 rounded-full mt-2"></div>
            <div>
              <p className="font-medium text-gray-900">Request Created</p>
              <p className="text-sm text-gray-500">{formatDateTime(request.createdAt)}</p>
            </div>
          </div>
          
          {request.tradeTeamReview && (
            <div className="flex items-start space-x-3">
              <div className={`w-2 h-2 rounded-full mt-2 ${
                request.tradeTeamReview.status === 'APPROVED' ? 'bg-success-500' : 'bg-danger-500'
              }`}></div>
              <div>
                <p className="font-medium text-gray-900">
                  Trade Team {request.tradeTeamReview.status === 'APPROVED' ? 'Approved' : 'Rejected'}
                </p>
                <p className="text-sm text-gray-500">{formatDateTime(request.tradeTeamReview.reviewedAt)}</p>
                {request.tradeTeamReview.comment && (
                  <p className="text-sm text-gray-600 mt-1">{request.tradeTeamReview.comment}</p>
                )}
              </div>
            </div>
          )}
          
          {request.treasuryReview && (
            <div className="flex items-start space-x-3">
              <div className={`w-2 h-2 rounded-full mt-2 ${
                request.treasuryReview.isSuccessful ? 'bg-success-500' : 'bg-danger-500'
              }`}></div>
              <div>
                <p className="font-medium text-gray-900">
                  Treasury {request.treasuryReview.isSuccessful ? 'Completed Successfully' : 'Marked Unsuccessful'}
                </p>
                <p className="text-sm text-gray-500">{formatDateTime(request.treasuryReview.finalizedAt)}</p>
                {request.treasuryReview.comment && (
                  <p className="text-sm text-gray-600 mt-1">{request.treasuryReview.comment}</p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Action Modal */}
      <Modal
        isOpen={isActionModalOpen}
        onClose={() => setIsActionModalOpen(false)}
        title={`${actionType === 'approve' ? 'Approve' : actionType === 'reject' ? 'Reject' : 'Finalize'} Request`}
        size="md"
      >
        <div className="space-y-4">
          {actionType === 'approve' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Transaction Reference *
              </label>
              <input
                type="text"
                value={transactionRef}
                onChange={(e) => setTransactionRef(e.target.value)}
                className="input-field"
                placeholder="Enter transaction reference number"
              />
            </div>
          )}

          {actionType === 'finalize' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="status"
                    checked={isSuccessful}
                    onChange={() => setIsSuccessful(true)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">Successful</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="status"
                    checked={!isSuccessful}
                    onChange={() => setIsSuccessful(false)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">Unsuccessful</span>
                </label>
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Comment {actionType === 'reject' ? '*' : '(Optional)'}
            </label>
            <textarea
              rows={3}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              className="input-field"
              placeholder={`Enter ${actionType} comment...`}
            />
          </div>

          <div className="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={() => setIsActionModalOpen(false)}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSubmitAction}
              className={actionType === 'approve' || actionType === 'finalize' ? 'btn-success' : 'btn-danger'}
            >
              {actionType === 'approve' ? 'Approve' : actionType === 'reject' ? 'Reject' : 'Finalize'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Confirmation Dialog */}
      <ConfirmDialog
        isOpen={isConfirmDialogOpen}
        onClose={() => setIsConfirmDialogOpen(false)}
        onConfirm={handleConfirmAction}
        title={`Confirm ${actionType === 'approve' ? 'Approval' : actionType === 'reject' ? 'Rejection' : 'Finalization'}`}
        message={`Are you sure you want to ${actionType} this request? This action cannot be undone.`}
        type={actionType === 'approve' || actionType === 'finalize' ? 'success' : 'danger'}
        confirmText={actionType === 'approve' ? 'Approve' : actionType === 'reject' ? 'Reject' : 'Finalize'}
        loading={isSubmitting}
      />
    </div>
  );
};

export default RequestDetails;
